#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试飞机号提取功能
"""

import os
import sys
import json
import tempfile
from datetime import datetime

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入主程序模块
from QAR_Work.PC_AUTO_BAK.PC_AUTO_BAK import FileProcessor, setup_logging

def create_test_msg_dat(folder_path, aircraft=None, date_str=None):
    """创建测试用的MSG.DAT文件"""
    msg_file = os.path.join(folder_path, "MSG.DAT")
    
    content = []
    if aircraft and date_str:
        # 标准格式
        content.append(f"DATE: {date_str[:2]}/{date_str[2:4]}/{date_str[4:6]} TIME: 12:30:45")
        content.append(f"A/C: {aircraft}")
        content.append("")
        
        # CC格式（作为备选）
        month_names = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN',
                      'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC']
        month_idx = int(date_str[4:6]) - 1
        month_name = month_names[month_idx] if 0 <= month_idx < 12 else 'JAN'
        day = date_str[6:8]
        content.append(f"CC {aircraft} {month_name}{day} 123045 ZUGY ZPJH 1807")
    
    with open(msg_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(content))

def test_aircraft_extraction():
    """测试飞机号提取功能"""
    print("=" * 60)
    print("测试飞机号提取功能")
    print("=" * 60)
    
    # 初始化日志
    logger = setup_logging()
    
    # 创建FileProcessor实例
    processor = FileProcessor()

    # 强制使用测试配置文件
    test_config_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "test_AC_TYPE.json")
    if os.path.exists(test_config_file):
        try:
            with open(test_config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)

            aircraft_list = []
            if isinstance(config_data, dict):
                for aircraft_code in config_data.keys():
                    if aircraft_code.startswith('B-') and len(aircraft_code) == 6:
                        aircraft_list.append(aircraft_code.upper())

            processor.aircraft_config = {
                'aircraft_list': aircraft_list,
                'config_data': config_data,
                'config_file': test_config_file
            }
            print(f"[DEBUG] 强制使用测试配置文件: {test_config_file}")
        except Exception as e:
            print(f"[DEBUG] 加载测试配置文件失败: {str(e)}")
    
    # 显示加载的配置信息
    if processor.aircraft_config:
        aircraft_list = processor.aircraft_config.get('aircraft_list', [])
        config_file = processor.aircraft_config.get('config_file', 'Unknown')
        print(f"\n✅ 成功加载配置文件: {config_file}")
        print(f"📋 配置中的飞机号数量: {len(aircraft_list)}")
        print(f"📋 前5个飞机号: {aircraft_list[:5]}")
    else:
        print("\n❌ 未能加载配置文件")
        return
    
    # 测试用例
    test_cases = [
        # (文件夹名, 期望的飞机号, 测试描述)
        ("B-1234_20250123", "B-1234", "完整格式 B-xxxx（在配置中）"),
        ("B1234_data_20250123", "B-1234", "无连字符格式 Bxxxx（在配置中）"),
        ("flight_1234_20250123", "B-1234", "仅后四位格式 xxxx（在配置中）"),
        ("B-9999_20250123", "B-9999", "完整格式（在配置中）"),
        ("B-ABCD_20250123", "B-ABCD", "不在配置中的飞机号，使用原始方法识别"),
        ("flight_1045_20250123", "B-1045", "原始方法：104x格式"),
        ("data_3456_info", "B-3456", "后四位格式（在配置中）"),
        ("unknown_folder", None, "无法识别的文件夹名"),
    ]
    
    print(f"\n🧪 开始测试 {len(test_cases)} 个用例:")
    print("-" * 60)
    
    for i, (folder_name, expected, description) in enumerate(test_cases, 1):
        print(f"\n测试 {i}: {description}")
        print(f"文件夹名: {folder_name}")
        print(f"期望结果: {expected}")
        
        # 执行提取
        result = processor.extract_aircraft_from_folder(folder_name)
        
        print(f"实际结果: {result}")
        
        # 检查结果
        if result == expected:
            print("✅ 测试通过")
        else:
            print("❌ 测试失败")
        
        print("-" * 40)
    
    # 测试综合判断功能
    print(f"\n🧪 测试综合判断功能:")
    print("-" * 60)
    
    # 创建临时目录进行测试
    with tempfile.TemporaryDirectory() as temp_dir:
        # 测试用例：文件夹名有配置中的飞机号，MSG.DAT有不同的飞机号
        test_folder = os.path.join(temp_dir, "B-1234_20250123")
        os.makedirs(test_folder)
        
        # 创建MSG.DAT文件，包含不同的飞机号
        create_test_msg_dat(test_folder, "B-5678", "20250124")
        
        print(f"\n测试场景: 文件夹名包含配置中的飞机号")
        print(f"文件夹名: B-1234_20250123 (B-1234在配置中)")
        print(f"MSG.DAT中: B-5678")
        
        folder_aircraft, folder_date = processor.extract_aircraft_and_date("B-1234_20250123", test_folder)
        
        print(f"最终飞机号: {folder_aircraft}")
        print(f"最终日期: {folder_date}")
        
        if folder_aircraft == "B-1234":
            print("✅ 正确选择了文件夹中的飞机号（在配置中找到）")
        else:
            print("❌ 飞机号选择错误")
        
        # 测试用例：文件夹名没有配置中的飞机号
        test_folder2 = os.path.join(temp_dir, "unknown_20250123")
        os.makedirs(test_folder2)
        create_test_msg_dat(test_folder2, "B-9999", "20250123")
        
        print(f"\n测试场景: 文件夹名不包含配置中的飞机号")
        print(f"文件夹名: unknown_20250123")
        print(f"MSG.DAT中: B-9999 (在配置中)")
        
        folder_aircraft2, folder_date2 = processor.extract_aircraft_and_date("unknown_20250123", test_folder2)
        
        print(f"最终飞机号: {folder_aircraft2}")
        print(f"最终日期: {folder_date2}")
        
        if folder_aircraft2 == "B-9999":
            print("✅ 正确选择了MSG.DAT中的飞机号")
        else:
            print("❌ 飞机号选择错误")

if __name__ == "__main__":
    test_aircraft_extraction()
