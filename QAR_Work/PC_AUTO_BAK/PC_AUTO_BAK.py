"""
PC_AUTO_BAK - 文件解压、重命名和复制工具
功能：监控指定路径，自动解压文件，重命名文件夹，复制到指定位置
"""

import os
import sys
import re
import shutil
import time
import zipfile
import threading
import logging
import json
import subprocess
import atexit  # 添加atexit模块用于注册退出处理函数
from datetime import datetime, timedelta
from pathlib import Path

# GUI相关导入
import tkinter as tk
from tkinter import ttk, messagebox

# 文件监控相关导入
try:
    from watchdog.observers import Observer
    from watchdog.events import FileSystemEventHandler
    WATCHDOG_AVAILABLE = True
except ImportError:
    WATCHDOG_AVAILABLE = False
    print("警告: watchdog库未安装，文件监控功能将不可用")

# 压缩文件处理相关导入
try:
    import rarfile
    RARFILE_AVAILABLE = True
except ImportError:
    RARFILE_AVAILABLE = False
    print("警告: rarfile库未安装，RAR文件解压功能将不可用")

try:
    import py7zr
    PY7ZR_AVAILABLE = True
except ImportError:
    PY7ZR_AVAILABLE = False
    print("警告: py7zr库未安装，7Z文件解压功能将不可用")

try:
    from pyunpack import Archive
    PYUNPACK_AVAILABLE = True
except ImportError:
    PYUNPACK_AVAILABLE = False
    print("警告: pyunpack库未安装，备选解压功能将不可用")

try:
    import patool
    PATOOL_AVAILABLE = True
except ImportError:
    PATOOL_AVAILABLE = False
    print("警告: patool库未安装，备选解压功能将不可用")

# 配置日志系统
def setup_logging():
    """设置日志系统"""
    log_dir = "D:\\AUTO_QAR"
    os.makedirs(log_dir, exist_ok=True)
    
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)
    logger.handlers.clear()
    
    # 主日志文件
    main_log_file = os.path.join(log_dir, 'PC_UNZIP.log')
    main_handler = logging.FileHandler(main_log_file, encoding='utf-8')
    main_handler.setFormatter(formatter)
    logger.addHandler(main_handler)
    
    # 控制台输出
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    return logger

# 初始化日志
logger = setup_logging()

# 常量定义
PC_MCC_PATH = r"Y:"
TEMP_PATH = r"Y:\Data_Monitor"
DATA_BAK_PATH = r"Z:\DATA_BAK\FDIMU_PC"
QAR_PC_PATH = r"Z:\DATA_BAK\QAR_PC"
QAR_PC_LOG_DIR = r"D:\AUTO_QAR"

# 测试环境路径（当Y盘不存在时使用）
TEST_PC_MCC_PATH = r"D:\test_pc_mcc"
TEST_TEMP_PATH = r"D:\test_pc_mcc\Data_Monitor"
TEST_DATA_BAK_PATH = r"D:\DATA_BAK\FDIMU_PC"
TEST_QAR_PC_PATH = r"D:\DATA_BAK\QAR_PC"

# 初始化实际路径变量
ACTUAL_PC_MCC_PATH = None
ACTUAL_TEMP_PATH = None
ACTUAL_DATA_BAK_PATH = None
ACTUAL_QAR_PC_PATH = None

def initialize_paths():
    """初始化实际可用的路径
    当Y盘不存在时，自动使用测试环境路径
    """
    global ACTUAL_PC_MCC_PATH, ACTUAL_TEMP_PATH, ACTUAL_DATA_BAK_PATH, ACTUAL_QAR_PC_PATH
    import os

    # 检查Y盘是否存在 (使用更安全的方式，避免直接访问可能不存在的路径)
    try:
        # 使用pathlib检查磁盘是否存在，更安全
        import pathlib
        y_drive = pathlib.Path('Y:')
        y_drive_exists = y_drive.exists()
    except:
        # 发生异常时假设Y盘不存在
        y_drive_exists = False

    if y_drive_exists:
        # Y盘存在，使用默认设置的路径
        ACTUAL_PC_MCC_PATH = PC_MCC_PATH
        ACTUAL_TEMP_PATH = TEMP_PATH
        ACTUAL_DATA_BAK_PATH = DATA_BAK_PATH
        ACTUAL_QAR_PC_PATH = QAR_PC_PATH
        print(f"✅ 使用当前设置的路径: {PC_MCC_PATH}")
    else:
        # Y盘不存在，使用测试环境路径
        ACTUAL_PC_MCC_PATH = TEST_PC_MCC_PATH
        ACTUAL_TEMP_PATH = TEST_TEMP_PATH
        ACTUAL_DATA_BAK_PATH = TEST_DATA_BAK_PATH
        ACTUAL_QAR_PC_PATH = TEST_QAR_PC_PATH
        print(f"⚠️ Y盘不存在，使用测试环境路径: {TEST_PC_MCC_PATH}")

class DateRangeManager:
    """日期范围管理器"""
    
    def __init__(self, scan_days=7):
        self.current_date = None
        self.date_range = None
        self.scan_days = scan_days  # 扫描天数，默认为7天
        self.update_date_range()
    
    def update_date_range(self):
        """更新日期范围"""
        today = datetime.now().date()
        
        # 如果日期发生变化或扫描天数改变，更新范围
        if self.current_date != today:
            self.current_date = today
            start_date = today - timedelta(days=self.scan_days - 1)  # 向前self.scan_days-1天，确保包含今天
            end_date = today                        # 到今天
            self.date_range = (start_date, end_date)
            
            logger.info(f"日期范围已更新: {start_date} 到 {end_date}")

    def set_scan_days(self, days):
        """设置扫描天数"""
        try:
            days = int(days)
            if days < 1:
                raise ValueError("扫描天数必须大于0")
            self.scan_days = days
            # 强制更新日期范围
            self.current_date = None
            self.update_date_range()
            return True
        except ValueError:
            logger.error(f"无效的扫描天数: {days}")
            return False
    
    def get_date_range(self):
        """获取当前日期范围"""
        self.update_date_range()
        return self.date_range
    
    def is_date_in_range(self, date_str):
        """判断日期是否在范围内"""
        try:
            # 解析日期字符串 (yyyymmdd格式)
            date_obj = datetime.strptime(date_str, '%Y%m%d').date()
            start_date, end_date = self.get_date_range()
            return start_date <= date_obj <= end_date
        except ValueError:
            return False
    
    def get_year_months_in_range(self):
        """获取日期范围内涉及的年月"""
        start_date, end_date = self.get_date_range()
        year_months = set()
        
        current = start_date.replace(day=1)
        while current <= end_date:
            year_months.add(current.strftime('%Y-%m'))
            # 移动到下个月
            if current.month == 12:
                current = current.replace(year=current.year + 1, month=1)
            else:
                current = current.replace(month=current.month + 1)
        
        return sorted(year_months)

class FileProcessor:
    """文件处理器"""

    def __init__(self, gui_callback=None):
        self.gui_callback = gui_callback
        # 获取程序所在目录（考虑PyInstaller打包情况）
        if hasattr(sys, '_MEIPASS'):
            # 打包模式下，使用程序所在目录
            base_dir = os.path.dirname(sys.executable)
        else:
            # 开发模式下，使用当前文件所在目录
            base_dir = os.path.dirname(os.path.abspath(__file__))
        self.config_file = os.path.join(base_dir, 'config.json')
        # 加载保存的PC卡计数
        self.processed_pc_cards = self.load_processed_pc_cards()
        # 增加PC卡计数用于测试保存功能
        self.processed_pc_cards += 10
        print(f"[DEBUG] 测试用：PC卡计数已增加到 {self.processed_pc_cards}")
        logger.info(f"测试用：PC卡计数已增加到 {self.processed_pc_cards}")
        self.errors = 0
        self.current_task = "待机中"

        # 加载飞机号配置文件
        self.aircraft_config = self.load_aircraft_config()

    def load_aircraft_config(self):
        """加载飞机号配置文件"""
        try:
            # AC_TYPE.json文件路径（支持测试环境）
            ac_type_file = r"Z:\DATA_BAK\config\AC_TYPE.json"

            # 如果Z盘文件不存在，尝试使用测试文件
            if not os.path.exists(ac_type_file):
                test_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "test_AC_TYPE.json")
                if os.path.exists(test_file):
                    ac_type_file = test_file
                    logger.info(f"使用测试配置文件: {ac_type_file}")
                    print(f"[DEBUG] 使用测试配置文件: {ac_type_file}")
                else:
                    logger.warning(f"飞机号配置文件不存在: {ac_type_file}")
                    print(f"[DEBUG] 飞机号配置文件不存在: {ac_type_file}")
                    return {}

            # 读取配置文件（尝试多种编码）
            config_data = None
            encodings_to_try = ['utf-8', 'utf-8-sig', 'gbk', 'latin1']

            for encoding in encodings_to_try:
                try:
                    with open(ac_type_file, 'r', encoding=encoding) as f:
                        config_data = json.load(f)
                    logger.info(f"成功使用编码 {encoding} 读取配置文件")
                    print(f"[DEBUG] 成功使用编码 {encoding} 读取配置文件")
                    break
                except (UnicodeDecodeError, json.JSONDecodeError) as e:
                    logger.warning(f"使用编码 {encoding} 读取失败: {str(e)}")
                    continue

            if config_data is None:
                logger.error("所有编码尝试失败，无法读取配置文件")
                return {}

            # 提取所有飞机号（假设配置文件格式为 {"B-1234": "型号", ...}）
            aircraft_list = []
            if isinstance(config_data, dict):
                for aircraft_code in config_data.keys():
                    if aircraft_code.startswith('B-') and len(aircraft_code) == 6:
                        aircraft_list.append(aircraft_code.upper())

            logger.info(f"成功加载飞机号配置，共 {len(aircraft_list)} 架飞机")
            print(f"[DEBUG] 加载的飞机号列表: {aircraft_list[:5]}..." if len(aircraft_list) > 5 else f"[DEBUG] 加载的飞机号列表: {aircraft_list}")

            return {
                'aircraft_list': aircraft_list,
                'config_data': config_data,
                'config_file': ac_type_file
            }

        except Exception as e:
            logger.error(f"加载飞机号配置文件失败: {str(e)}")
            print(f"[DEBUG] 加载飞机号配置文件失败: {str(e)}")
            return {}

    def load_processed_pc_cards(self):
        """加载保存的PC卡计数"""
        try:
            print(f"[DEBUG] 尝试加载PC卡计数配置文件: {self.config_file}")
            logger.info(f"尝试加载PC卡计数配置文件: {self.config_file}")
            if os.path.exists(self.config_file):
                print(f"[DEBUG] 配置文件存在，开始读取")
                logger.info(f"配置文件存在，开始读取")
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    print(f"[DEBUG] 成功加载PC卡计数: {config.get('processed_pc_cards', 0)}")
                    logger.info(f"成功加载PC卡计数: {config.get('processed_pc_cards', 0)}")
                    return config.get('processed_pc_cards', 0)
            else:
                print(f"[DEBUG] 配置文件不存在，返回默认值0")
                logger.info(f"配置文件不存在，返回默认值0")
                return 0
        except Exception as e:
            print(f"[DEBUG] 加载PC卡计数失败: {str(e)}")
            logger.error(f"加载PC卡计数失败: {str(e)}")
            return 0
        
    def save_processed_pc_cards(self):
        """保存PC卡计数"""
        try:
            print(f"[DEBUG] 开始保存PC卡计数到配置文件: {self.config_file}")
            logger.info(f"开始保存PC卡计数到配置文件: {self.config_file}")
            print(f"[DEBUG] 当前PC卡计数: {self.processed_pc_cards}")
            logger.info(f"当前PC卡计数: {self.processed_pc_cards}")
            config = {
                'processed_pc_cards': self.processed_pc_cards,
                'last_updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            print(f"[DEBUG] 准备保存的配置: {config}")
            logger.info(f"准备保存的配置: {config}")
            
            # 检查目录是否存在
            config_dir = os.path.dirname(self.config_file)
            if not os.path.exists(config_dir):
                print(f"[DEBUG] 配置文件目录不存在，创建目录: {config_dir}")
                logger.info(f"配置文件目录不存在，创建目录: {config_dir}")
                os.makedirs(config_dir, exist_ok=True)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            print(f"[DEBUG] PC卡计数已成功保存: {self.processed_pc_cards}")
            logger.info(f"PC卡计数已成功保存: {self.processed_pc_cards}")
            # 验证保存结果
            with open(self.config_file, 'r', encoding='utf-8') as f:
                saved_config = json.load(f)
                print(f"[DEBUG] 验证保存结果: {saved_config}")
                logger.info(f"验证保存结果: {saved_config}")
        except Exception as e:
            print(f"[DEBUG] 保存PC卡计数失败: {str(e)}")
            logger.error(f"保存PC卡计数失败: {str(e)}")
            import traceback
            print(f"[DEBUG] 详细错误信息: {traceback.format_exc()}")
            logger.error(f"详细错误信息: {traceback.format_exc()}")

    def __del__(self):
        """析构函数，确保在对象销毁时保存PC卡计数"""
        try:
            print(f"[DEBUG] FileProcessor对象被销毁，尝试保存PC卡计数")
            logger.info(f"FileProcessor对象被销毁，尝试保存PC卡计数")
            self.save_processed_pc_cards()
        except Exception as e:
            print(f"[DEBUG] 析构函数中保存PC卡计数失败: {str(e)}")
            logger.error(f"析构函数中保存PC卡计数失败: {str(e)}")
    
    def update_gui(self, task, pc_cards=None, errors=None):
        """更新GUI"""
        self.current_task = task
        if pc_cards is not None:
            self.processed_pc_cards = pc_cards
        if errors is not None:
            self.errors = errors

        if self.gui_callback:
            try:
                # 检查gui_callback是否有update_gui_callback方法
                if hasattr(self.gui_callback, 'update_gui_callback'):
                    self.gui_callback.update_gui_callback(task, self.processed_pc_cards, self.errors)
                else:
                    self.gui_callback(task, self.processed_pc_cards, self.errors)
            except Exception as e:
                logger.error(f"GUI更新失败: {str(e)}")
    
    def copy_file_or_folder(self, src, dst, log_file, timeout_seconds=300):
        """复制文件或文件夹并记录日志（带超时和重试机制）"""
        # 路径安全检查
        if not os.path.exists(src):
            logger.error(f"源路径不存在: {src}")
            return False

        file_name = os.path.basename(src)

        # 首先检查是否已经处理过且成功
        processed_files = self.read_processed_files(log_file)
        if file_name in processed_files:
            status = processed_files[file_name]
            if status == '成功':
                logger.info(f"文件已成功处理过，跳过: {file_name}")
                return True
            else:
                logger.info(f"文件之前处理失败，重新尝试: {file_name}")

        max_retries = 3
        retry_delay = 1  # 秒

        for attempt in range(max_retries):
            try:
                if os.path.isfile(src):
                    # 复制文件
                    os.makedirs(os.path.dirname(dst), exist_ok=True)
                    shutil.copy2(src, dst)
                    file_type = "File"
                    # 如果是压缩文件，计入PC卡统计
                    if src.lower().endswith(('.zip', '.rar', '.7z')):
                        self.processed_pc_cards += 1
                else:
                    # 复制文件夹（改进的逻辑）
                    if os.path.exists(dst):
                        # 尝试多次删除
                        for del_attempt in range(3):
                            try:
                                shutil.rmtree(dst)
                                break
                            except OSError:
                                if del_attempt < 2:
                                    time.sleep(0.5)
                                else:
                                    raise

                    shutil.copytree(src, dst)
                    file_type = "Folder"
                    # 文件夹计入PC卡统计（但不在这里统计，在数据处理阶段统计）
                    pass

                # 记录到日志（每个文件只记录一次）
                self.add_processed_file_log(file_name, file_type, log_file)

                return True

            except (OSError, PermissionError) as e:
                if attempt < max_retries - 1:
                    logger.warning(f"复制失败，重试 {attempt + 1}/{max_retries}: {str(e)}")
                    time.sleep(retry_delay)
                    retry_delay *= 2  # 指数退避
                    continue
                else:
                    # 最后一次尝试失败
                    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    item_type = 'File' if os.path.isfile(src) else 'Folder'
                    log_entry = f"{timestamp}\t{os.path.basename(src)}\t{item_type}\tFailed: {str(e)}\n"

                    # 写入日志
                    with open(log_file, 'a', encoding='utf-8') as f:
                        f.write(log_entry)

                    main_log = os.path.join(QAR_PC_LOG_DIR, 'QAR_PC.log')
                    with open(main_log, 'a', encoding='utf-8') as f:
                        f.write(log_entry)

                    self.errors += 1
                    logger.error(f"复制失败（重试{max_retries}次后）: {src} -> {dst}, 错误: {str(e)}")
                    return False
    
    def read_processed_files(self, log_file):
        """读取已处理的文件列表（返回文件名和状态的字典）"""
        processed = {}
        if os.path.exists(log_file):
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        parts = line.strip().split('\t')
                        if len(parts) >= 4:
                            file_name = parts[1]
                            if parts[3] == 'Success':
                                processed[file_name] = '成功'
                            elif parts[3].startswith('Failed'):
                                processed[file_name] = '失败'
            except Exception as e:
                logger.error(f"读取日志文件失败: {str(e)}")
        return processed

    def is_file_processed(self, file_name, log_file):
        """检查文件是否已经成功处理过"""
        if not os.path.exists(log_file):
            return False

        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    parts = line.strip().split('\t')
                    if len(parts) >= 4 and parts[1] == file_name and parts[3] == 'Success':
                        return True
        except Exception as e:
            logger.error(f"检查文件处理状态失败: {str(e)}")

        return False

    def add_processed_file_log(self, file_name, file_type, log_file):
        """添加文件处理记录到日志（每个文件只记录一次）"""
        try:
            # 检查是否已经记录过
            if self.is_file_processed(file_name, log_file):
                return  # 已经记录过，不重复记录

            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            log_entry = f"{timestamp}\t{file_name}\t{file_type}\tSuccess\n"

            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(log_entry)

            # 同时写入主日志
            main_log = os.path.join(QAR_PC_LOG_DIR, 'QAR_PC.log')
            with open(main_log, 'a', encoding='utf-8') as f:
                f.write(log_entry)

        except Exception as e:
            logger.error(f"写入日志失败: {str(e)}")
    
    def extract_archive(self, archive_path, extract_dir):
        """解压压缩文件（改进版）"""
        try:
            # 安全检查
            if not os.path.exists(archive_path):
                logger.error(f"压缩文件不存在: {archive_path}")
                return False

            # 检查磁盘空间（检查父目录）
            parent_dir = os.path.dirname(extract_dir)
            if not self.check_disk_space(parent_dir, 0.1):  # 至少100MB
                logger.error(f"解压目录磁盘空间不足: {extract_dir}")
                return False

            file_ext = os.path.splitext(archive_path)[1].lower()
            logger.info(f"开始解压: {archive_path} -> {extract_dir}")

            success = False

            if file_ext == '.zip':
                with zipfile.ZipFile(archive_path, 'r') as zip_ref:
                    zip_ref.extractall(extract_dir)
                    logger.info(f"ZIP解压成功: {len(zip_ref.namelist())} 个文件")
                    success = True

            elif file_ext == '.rar':
                success = self.extract_rar_file(archive_path, extract_dir)

            elif file_ext == '.7z':
                success = self.extract_7z_file(archive_path, extract_dir)
            else:
                logger.warning(f"不支持的压缩格式: {file_ext}")
                return False

            # 验证解压结果
            if success and os.path.exists(extract_dir) and os.listdir(extract_dir):
                # 解压成功后删除原文件
                os.remove(archive_path)
                logger.info(f"解压完成并删除原文件: {archive_path}")
                return True
            else:
                logger.error(f"解压失败或解压后目录为空: {extract_dir}")
                return False

        except Exception as e:
            logger.error(f"解压失败: {archive_path}, 错误: {str(e)}")
            self.errors += 1
            return False

    def extract_rar_file(self, archive_path, extract_dir):
        """解压RAR文件"""
        try:
            # 首先尝试使用rarfile库
            if RARFILE_AVAILABLE:
                try:
                    with rarfile.RarFile(archive_path, 'r') as rar_ref:
                        rar_ref.extractall(extract_dir)
                        logger.info(f"RAR解压成功: {len(rar_ref.namelist())} 个文件")
                        return True
                except Exception as e:
                    logger.warning(f"rarfile库解压失败: {str(e)}，尝试pyunpack库")

            # 尝试使用pyunpack库
            if PYUNPACK_AVAILABLE:
                try:
                    Archive(archive_path).extractall(extract_dir)
                    logger.info(f"pyunpack解压RAR成功: {archive_path}")
                    return True
                except Exception as e:
                    logger.warning(f"pyunpack库解压失败: {str(e)}，尝试patool库")

            # 尝试使用patool库
            if PATOOL_AVAILABLE:
                try:
                    patool.extract_archive(archive_path, outdir=extract_dir)
                    logger.info(f"patool解压RAR成功: {archive_path}")
                    return True
                except Exception as e:
                    logger.warning(f"patool库解压失败: {str(e)}，尝试WinRAR命令行")

            # 最后尝试使用WinRAR命令行
            return self.extract_with_winrar(archive_path, extract_dir)

        except Exception as e:
            logger.error(f"RAR解压异常: {str(e)}")
            return False

    def extract_7z_file(self, archive_path, extract_dir):
        """解压7Z文件"""
        try:
            # 首先尝试使用py7zr库
            if PY7ZR_AVAILABLE:
                try:
                    with py7zr.SevenZipFile(archive_path, mode='r') as z:
                        z.extractall(extract_dir)
                        logger.info(f"7Z解压成功")
                        return True
                except Exception as e:
                    logger.warning(f"py7zr解压失败: {str(e)}，尝试pyunpack库")

            # 尝试使用pyunpack库
            if PYUNPACK_AVAILABLE:
                try:
                    Archive(archive_path).extractall(extract_dir)
                    logger.info(f"pyunpack解压7Z成功: {archive_path}")
                    return True
                except Exception as e:
                    logger.warning(f"pyunpack库解压失败: {str(e)}，尝试patool库")

            # 尝试使用patool库
            if PATOOL_AVAILABLE:
                try:
                    patool.extract_archive(archive_path, outdir=extract_dir)
                    logger.info(f"patool解压7Z成功: {archive_path}")
                    return True
                except Exception as e:
                    logger.warning(f"patool库解压失败: {str(e)}，尝试7-Zip命令行")

            # 最后尝试使用7-Zip命令行
            return self.extract_with_7zip(archive_path, extract_dir)

        except Exception as e:
            logger.error(f"7Z解压异常: {str(e)}")
            return False

    def extract_with_winrar(self, archive_path, extract_dir):
        """使用WinRAR命令行解压"""
        try:
            import subprocess
            # 常见的WinRAR安装路径
            winrar_paths = [
                r"C:\Program Files\WinRAR\WinRAR.exe",
                r"C:\Program Files (x86)\WinRAR\WinRAR.exe"
            ]

            winrar_exe = None
            for path in winrar_paths:
                if os.path.exists(path):
                    winrar_exe = path
                    break

            if not winrar_exe:
                logger.error("未找到WinRAR程序")
                return False

            # 执行解压命令（后台模式，不弹窗）
            cmd = [winrar_exe, 'x', '-y', '-ibck', archive_path, extract_dir + '\\']
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300,
                                  creationflags=subprocess.CREATE_NO_WINDOW)

            if result.returncode == 0:
                logger.info(f"WinRAR解压成功: {archive_path}")
                return True
            else:
                logger.error(f"WinRAR解压失败: {result.stderr}")
                return False

        except Exception as e:
            logger.error(f"WinRAR解压异常: {str(e)}")
            return False

    def extract_with_7zip(self, archive_path, extract_dir):
        """使用7-Zip命令行解压"""
        try:
            import subprocess
            # 常见的7-Zip安装路径
            sevenzip_paths = [
                r"C:\Program Files\7-Zip\7z.exe",
                r"C:\Program Files (x86)\7-Zip\7z.exe"
            ]

            sevenzip_exe = None
            for path in sevenzip_paths:
                if os.path.exists(path):
                    sevenzip_exe = path
                    break

            if not sevenzip_exe:
                logger.error("未找到7-Zip程序")
                return False

            # 执行解压命令
            cmd = [sevenzip_exe, 'x', archive_path, f'-o{extract_dir}', '-y']
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)

            if result.returncode == 0:
                logger.info(f"7-Zip解压成功: {archive_path}")
                return True
            else:
                logger.error(f"7-Zip解压失败: {result.stderr}")
                return False

        except Exception as e:
            logger.error(f"7-Zip解压异常: {str(e)}")
            return False

    def cleanup_temp_files(self, temp_date_path, date_str):
        """清理临时文件和文件夹"""
        try:
            if not os.path.exists(temp_date_path):
                return

            # 检查该日期的所有文件是否都处理成功
            all_success = True
            failed_files = []

            # 遍历临时目录中的所有文件和文件夹
            for item in os.listdir(temp_date_path):
                item_path = os.path.join(temp_date_path, item)

                # 检查是否为数据文件夹（包含DAR.DAT等）
                if os.path.isdir(item_path):
                    if self.is_valid_data_folder(item_path):
                        # 检查数据文件夹是否处理成功
                        aircraft, date_info = self.extract_aircraft_and_date(item, item_path)
                        if not aircraft or not date_info:
                            logger.warning(f"数据文件夹处理失败，保留: {item}")
                            all_success = False
                            failed_files.append(item)
                            continue

                        # 检查是否已复制到目标目录
                        data_bak_path = self.get_data_bak_path(aircraft, date_info)
                        qar_pc_path = self.get_qar_pc_path(aircraft, date_info)

                        if not (os.path.exists(data_bak_path) and os.path.exists(qar_pc_path)):
                            logger.warning(f"目标目录不存在，数据文件夹处理失败，保留: {item}")
                            all_success = False
                            failed_files.append(item)

                # 压缩文件应该已经被解压并删除，如果还存在说明解压失败
                elif item.lower().endswith(('.zip', '.rar', '.7z')):
                    logger.warning(f"压缩文件未被删除，解压可能失败，保留: {item}")
                    all_success = False
                    failed_files.append(item)

            if all_success:
                # 所有文件都处理成功，删除整个日期文件夹
                shutil.rmtree(temp_date_path)
                logger.info(f"所有文件处理成功，删除临时日期文件夹: {temp_date_path}")
            else:
                # 有文件处理失败，只删除成功处理的文件，保留失败的
                for item in os.listdir(temp_date_path):
                    if item not in failed_files:
                        item_path = os.path.join(temp_date_path, item)
                        try:
                            if os.path.isdir(item_path):
                                shutil.rmtree(item_path)
                            else:
                                os.remove(item_path)
                            logger.info(f"删除成功处理的文件: {item}")
                        except Exception as e:
                            logger.error(f"删除文件失败: {item}, 错误: {str(e)}")

                logger.info(f"部分文件处理失败，保留失败文件: {failed_files}")

        except Exception as e:
            logger.error(f"清理临时文件失败: {str(e)}")

    def get_data_bak_path(self, aircraft, date_info):
        """获取DATA_BAK目标路径"""
        try:
            year = date_info[:4]
            month_day = f"{date_info[4:6]}-{date_info[6:8]}"
            return os.path.join(ACTUAL_DATA_BAK_PATH, year, aircraft, month_day)
        except:
            return ""

    def get_qar_pc_path(self, aircraft, date_info):
        """获取QAR_PC目标路径"""
        try:
            new_folder_name = f"{aircraft}_{date_info}0000.pc"
            return os.path.join(ACTUAL_QAR_PC_PATH, new_folder_name)
        except:
            return ""

    def extract_aircraft_and_date(self, folder_name, folder_path):
        """从文件夹名和MSG.DAT文件中提取飞机号和日期信息（改进版）"""
        # 从文件夹名提取飞机号
        folder_aircraft = self.extract_aircraft_from_folder(folder_name)

        # 从文件夹名提取日期
        folder_date = self.extract_date_from_folder(folder_name)

        # 从MSG.DAT文件中提取信息
        msg_aircraft, msg_date = self.extract_from_msg_dat(folder_path)

        # 综合判断最终的飞机号
        # 如果文件夹找到的飞机号在AC_TYPE.json中有对应信息，则文件名优先，否则MSG.DAT优先
        final_aircraft = None
        if folder_aircraft and self.is_aircraft_in_config(folder_aircraft):
            final_aircraft = folder_aircraft
            logger.info(f"使用文件夹飞机号（在配置中找到）: {folder_aircraft}")
            print(f"[DEBUG] 使用文件夹飞机号（在配置中找到）: {folder_aircraft}")
        elif msg_aircraft:
            final_aircraft = msg_aircraft
            logger.info(f"使用MSG.DAT飞机号: {msg_aircraft}")
            print(f"[DEBUG] 使用MSG.DAT飞机号: {msg_aircraft}")
        elif folder_aircraft:
            final_aircraft = folder_aircraft
            logger.info(f"使用文件夹飞机号（配置中未找到但作为备选）: {folder_aircraft}")
            print(f"[DEBUG] 使用文件夹飞机号（配置中未找到但作为备选）: {folder_aircraft}")

        # 日期优先级：如果MSG.DAT日期较文件夹日期更靠后，使用文件夹日期，否则使用MSG.DAT日期
        final_date = None
        if folder_date and msg_date:
            try:
                folder_date_obj = datetime.strptime(folder_date, '%Y%m%d')
                msg_date_obj = datetime.strptime(msg_date, '%Y%m%d')

                if msg_date_obj > folder_date_obj:
                    logger.info(f"MSG.DAT日期({msg_date})较文件夹日期({folder_date})更靠后，使用文件夹日期")
                    print(f"[DEBUG] MSG.DAT日期({msg_date})较文件夹日期({folder_date})更靠后，使用文件夹日期")
                    final_date = folder_date
                else:
                    logger.info(f"使用MSG.DAT日期: {msg_date}")
                    print(f"[DEBUG] 使用MSG.DAT日期: {msg_date}")
                    final_date = msg_date
            except ValueError:
                # 日期格式错误时，优先使用文件夹日期
                final_date = folder_date or msg_date
                logger.info(f"日期格式错误，使用备选日期: {final_date}")
                print(f"[DEBUG] 日期格式错误，使用备选日期: {final_date}")
        else:
            final_date = folder_date or msg_date
            logger.info(f"使用可用日期: {final_date}")
            print(f"[DEBUG] 使用可用日期: {final_date}")

        return final_aircraft, final_date

    def is_aircraft_in_config(self, aircraft_code):
        """检查飞机号是否在配置文件中"""
        if not self.aircraft_config or 'aircraft_list' not in self.aircraft_config:
            return False

        aircraft_list = self.aircraft_config['aircraft_list']
        return aircraft_code.upper() in [ac.upper() for ac in aircraft_list]

    def extract_aircraft_from_folder(self, folder_name):
        """从文件夹名提取飞机号（改进版：优先使用AC_TYPE.json配置）"""
        folder_upper = folder_name.upper()

        # 获取配置文件中的飞机号列表
        aircraft_list = []
        if self.aircraft_config and 'aircraft_list' in self.aircraft_config:
            aircraft_list = self.aircraft_config['aircraft_list']

        logger.info(f"开始从文件夹名提取飞机号: {folder_name}")
        print(f"[DEBUG] 文件夹名: {folder_name}, 配置中飞机号数量: {len(aircraft_list)}")

        # 如果有配置文件，使用配置文件中的飞机号进行匹配
        if aircraft_list:
            # 第一优先级：B-xxxx 格式（完整格式）
            for aircraft in aircraft_list:
                if aircraft in folder_upper:
                    logger.info(f"找到完整格式飞机号: {aircraft}")
                    print(f"[DEBUG] 找到完整格式飞机号: {aircraft}")
                    return aircraft

            # 第二优先级：Bxxxx 格式（无连字符）
            for aircraft in aircraft_list:
                aircraft_no_dash = aircraft.replace('-', '')  # B1234
                if aircraft_no_dash in folder_upper:
                    logger.info(f"找到无连字符格式飞机号: {aircraft_no_dash} -> {aircraft}")
                    print(f"[DEBUG] 找到无连字符格式飞机号: {aircraft_no_dash} -> {aircraft}")
                    return aircraft

            # 第三优先级：xxxx 格式（仅后四位）
            for aircraft in aircraft_list:
                aircraft_suffix = aircraft[2:]  # 去掉B-，得到后四位
                if aircraft_suffix in folder_upper:
                    logger.info(f"找到后四位格式飞机号: {aircraft_suffix} -> {aircraft}")
                    print(f"[DEBUG] 找到后四位格式飞机号: {aircraft_suffix} -> {aircraft}")
                    return aircraft

        # 如果配置文件匹配失败，使用原来的方法
        logger.info("配置文件匹配失败，使用原始方法提取飞机号")
        print(f"[DEBUG] 配置文件匹配失败，使用原始方法")
        return self.extract_aircraft_from_folder_original(folder_name)

    def extract_aircraft_from_folder_original(self, folder_name):
        """从文件夹名提取飞机号（原始方法，作为备用）"""
        folder_upper = folder_name.upper()

        # 匹配 B-xxxx, b-xxxx, Bxxxx, bxxxx 格式
        patterns = [
            r'B-([A-Z0-9]{4})',  # B-xxxx
            r'B([A-Z0-9]{4})',   # Bxxxx
        ]

        for pattern in patterns:
            match = re.search(pattern, folder_upper)
            if match:
                return f"B-{match.group(1)}"

        # 查找 104x 或 104X（B-104X支持）
        if re.search(r'104[A-Z0-9]', folder_upper):
            match = re.search(r'(104[A-Z0-9])', folder_upper)
            if match:
                return f"B-{match.group(1)}"

        # 查找 1xxx, 3xxx, 6xxx, 8xxx 格式（增加B-3xxx支持）
        patterns_short = [
            r'(1[A-Z0-9]{3})',
            r'(3[A-Z0-9]{3})',
            r'(6[A-Z0-9]{3})',
            r'(8[A-Z0-9]{3})',
        ]

        for pattern in patterns_short:
            match = re.search(pattern, folder_upper)
            if match:
                return f"B-{match.group(1)}"

        return None

    def extract_date_from_folder(self, folder_name):
        """从文件夹名提取日期信息"""
        current_year = datetime.now().year
        current_month = datetime.now().month

        # 各种日期格式的正则表达式
        date_patterns = [
            (r'(\d{4})\.(\d{1,2})\.(\d{1,2})', '%Y-%m-%d'),      # yyyy.mm.dd, yyyy.m.d
            (r'(\d{4})-(\d{1,2})-(\d{1,2})', '%Y-%m-%d'),        # yyyy-mm-dd, yyyy-m-d
            (r'(\d{4})(\d{2})(\d{2})', '%Y-%m-%d'),              # yyyymmdd
            (r'(\d{4})(\d{1})(\d{1,2})', '%Y-%m-%d'),            # yyyymd
            (r'(\d{2})(\d{2})', None),                           # mmdd
            (r'(\d{1,2})\.(\d{1,2})', None),                     # m.d
        ]

        for pattern, format_str in date_patterns:
            match = re.search(pattern, folder_name)
            if match:
                groups = match.groups()

                if len(groups) == 3:  # 完整日期
                    try:
                        year, month, day = int(groups[0]), int(groups[1]), int(groups[2])
                        return f"{year:04d}{month:02d}{day:02d}"
                    except ValueError:
                        continue

                elif len(groups) == 2:  # mmdd 或 m.d 格式
                    try:
                        month, day = int(groups[0]), int(groups[1])

                        # 判断年份：如果月份是12月而当前是1月，使用上一年
                        if month == 12 and current_month == 1:
                            year = current_year - 1
                        else:
                            year = current_year

                        return f"{year:04d}{month:02d}{day:02d}"
                    except ValueError:
                        continue

        return None
        
    def extract_date_with_parent_folders(self, folder_path):
        """从文件夹路径及其父目录中提取日期信息
        如果当前文件夹无法获取日期，则向上级目录查找，直至根目录
        """
        # 首先尝试从当前文件夹名提取日期
        current_folder = os.path.basename(folder_path)
        date = self.extract_date_from_folder(current_folder)
        if date:
            return date

        # 如果当前文件夹没有，向上级目录查找
        parent_folder = os.path.dirname(folder_path)
        while parent_folder and parent_folder != os.path.dirname(parent_folder):  # 直到根目录
            current_folder = os.path.basename(parent_folder)
            date = self.extract_date_from_folder(current_folder)
            if date:
                return date
            parent_folder = os.path.dirname(parent_folder)

        return None

    def extract_from_msg_dat(self, folder_path):
        """从MSG.DAT文件中提取飞机号和日期信息（完全使用PC_BAK.py的方法）"""
        msg_file = os.path.join(folder_path, "MSG.DAT")

        if not os.path.exists(msg_file):
            return None, None

        try:
            # 使用PC_BAK.py的parse_mixed_format_file方法
            record, total_records = self.parse_mixed_format_file(msg_file)

            if not record:
                return None, None

            # 转换UTC时间为北京时间（如PC_BAK.py）
            beijing_date, beijing_time = self.convert_utc_to_beijing(record['date'], record['time'])
            if not beijing_date:
                return None, None

            # 转换日期格式为YYYYMMDD
            try:
                date_obj = datetime.strptime(beijing_date, '%Y-%m-%d')
                date_info = date_obj.strftime('%Y%m%d')
            except Exception as e:
                logger.error(f"日期格式转换失败: {beijing_date}, 错误: {str(e)}")
                return None, None

            aircraft = record['ac'] if record['ac'] and record['ac'] != 'None' else None

            return aircraft, date_info

        except Exception as e:
            logger.error(f"读取MSG.DAT文件失败: {str(e)}")
            return None, None

    def parse_mixed_format_file(self, file_path):
        """解析混合格式文件（完全使用PC_BAK.py的方法）"""
        try:
            file_size = os.path.getsize(file_path)
            file_size_mb = file_size / (1024 * 1024)
            logger.info(f"  文件大小: {file_size_mb:.2f} MB")

            # 尝试多种编码方式
            encodings_to_try = ['utf-8', 'latin1', 'cp1252', 'utf-8-sig', 'ascii']

            best_records = []
            best_encoding = None

            for encoding in encodings_to_try:
                try:
                    records = self.extract_records_with_encoding(file_path, encoding)
                    if records and len(records) > len(best_records):
                        best_records = records
                        best_encoding = encoding
                        # 如果找到足够多的记录，就使用这个编码
                        if len(records) > 50:
                            break
                except Exception as e:
                    continue

            # 检查是否需要启动第二种识别方式（关键！）
            has_valid_aircraft = False
            has_valid_date = False

            if best_records:
                # 检查是否有有效的飞机号和日期信息
                for record in best_records:
                    if record.get('ac') and record.get('ac') != 'None':
                        has_valid_aircraft = True
                    if record.get('date'):
                        has_valid_date = True
                    if has_valid_aircraft and has_valid_date:
                        break

            # 如果现有方法没有找到完整信息，启动第二种识别方式
            cc_records = []
            if not best_records or not (has_valid_aircraft and has_valid_date):
                logger.info(f"  现有方法未找到完整信息，启动CC B-关键字搜索...")
                try:
                    cc_records = self.parse_cc_format(file_path, best_encoding or 'latin1')
                    if cc_records:
                        logger.info(f"  CC格式解析找到 {len(cc_records)} 条记录")
                    else:
                        logger.info(f"  CC格式解析未找到记录")
                except Exception as e:
                    logger.warning(f"  CC格式解析失败: {str(e)}")

            # 合并所有记录
            all_records = []
            if best_records:
                all_records.extend(best_records)
            if cc_records:
                all_records.extend(cc_records)

            if not all_records:
                logger.warning(f"  未找到任何有效记录")
                return None, 0

            logger.info(f"  使用编码: {best_encoding}, 找到 {len(all_records)} 条记录")

            # 按时间排序，取最新的
            all_records.sort(key=lambda x: x.get('datetime', datetime.min), reverse=True)
            result = all_records[0]

            logger.info(f"  最新记录: 飞机号={result.get('ac', 'None')}, 日期={result.get('date', 'None')}, 时间={result.get('time', 'None')}")

            return result, len(all_records)

        except Exception as e:
            logger.error(f"  文件解析异常: {str(e)}")
            import traceback
            logger.error(f"  详细错误: {traceback.format_exc()}")
            return None, 0

    def extract_records_with_encoding(self, file_path, encoding):
        """使用指定编码提取记录 - 改进版本处理飞机号缺失的情况"""
        # 预编译正则表达式
        date_time_pattern = re.compile(r'DATE:\s*(\d{2})/(\d{2})/(\d{2})\s+TIME:\s*(\d{2}:\d{2}:\d{2})', re.I)
        ac_pattern = re.compile(r'A/C:\s*(B-[A-Z0-9]{4})', re.I)
        ac_empty_pattern = re.compile(r"A/C:\s*['\s]*", re.I)  # 匹配空的飞机号

        complete_records = []  # 有完整飞机号的记录
        time_only_records = []  # 只有时间没有飞机号的记录
        valid_aircraft_codes = set()  # 收集所有有效的飞机号

        line_count = 0
        current_record = {}

        with open(file_path, 'r', encoding=encoding, errors='ignore') as f:
            for line in f:
                line_count += 1

                # 跳过二进制行
                if self.is_binary_line(line):
                    continue

                line = line.strip()
                if not line:
                    continue

                # 查找日期时间行
                date_time_match = date_time_pattern.search(line)
                if date_time_match:
                    year, month, day, time = date_time_match.groups()
                    current_record = {
                        'date': f"{year}/{month}/{day}",
                        'time': time,
                        'line_num': line_count,
                        'raw_line': line
                    }
                    continue

                # 查找飞机号行
                if current_record:
                    ac_match = ac_pattern.search(line)
                    if ac_match:
                        aircraft = ac_match.group(1).upper()
                        current_record['ac'] = aircraft
                        valid_aircraft_codes.add(aircraft)

                        # 转换为datetime对象（注意：MSG.DAT中的日期格式是yy/mm/dd）
                        date_parts = current_record['date'].split('/')
                        dt = self.convert_to_datetime(
                            date_parts[0],  # year (yy)
                            date_parts[1],  # month (mm)
                            date_parts[2],  # day (dd)
                            current_record['time']
                        )
                        if dt:
                            current_record['datetime'] = dt
                            complete_records.append(current_record.copy())
                        current_record = {}
                        continue

                    # 检查是否是空的飞机号行
                    if ac_empty_pattern.search(line):
                        # 转换为datetime对象（注意：MSG.DAT中的日期格式是yy/mm/dd）
                        date_parts = current_record['date'].split('/')
                        dt = self.convert_to_datetime(
                            date_parts[0],  # year (yy)
                            date_parts[1],  # month (mm)
                            date_parts[2],  # day (dd)
                            current_record['time']
                        )
                        if dt:
                            current_record['datetime'] = dt
                            current_record['ac'] = None
                            time_only_records.append(current_record.copy())
                        current_record = {}
                        continue

        # 如果有完整记录，优先返回
        if complete_records:
            return complete_records

        # 如果只有时间记录，尝试用最常见的飞机号补充
        if time_only_records and valid_aircraft_codes:
            most_common_aircraft = max(valid_aircraft_codes, key=lambda x: sum(1 for r in complete_records if r.get('ac') == x)) if complete_records else list(valid_aircraft_codes)[0]
            for record in time_only_records:
                record['ac'] = most_common_aircraft
            return time_only_records

        return time_only_records if time_only_records else []

    def is_binary_line(self, line):
        """判断一行是否为二进制数据"""
        if len(line) < 5:
            return False

        # 计算非打印字符的比例
        non_printable_count = sum(1 for c in line if ord(c) < 32 or ord(c) > 126)
        non_printable_ratio = non_printable_count / len(line)

        # 如果非打印字符超过30%，认为是二进制行
        return non_printable_ratio > 0.3

    def convert_to_datetime(self, year, month, day, time_str):
        """将日期时间字符串转换为datetime对象"""
        try:
            year_int = int(year)
            # 修正年份转换逻辑：对于航班数据，69应该是1969年，不是2069年
            if year_int < 50:  # 00-49 认为是20xx年
                full_year = 2000 + year_int
            elif year_int < 100:  # 50-99 认为是19xx年
                full_year = 1900 + year_int
            else:
                full_year = year_int

            month = month.zfill(2)
            day = day.zfill(2)

            dt = datetime.strptime(f"{full_year}-{month}-{day} {time_str}", "%Y-%m-%d %H:%M:%S")
            return dt
        except Exception as e:
            return None





    def convert_utc_to_beijing(self, date_str, time_str):
        """将UTC时间转换为北京时间（完全使用PC_BAK.py的方法）"""
        try:
            # 处理不同的日期格式
            if '/' in date_str:
                # 处理 yy/mm/dd 格式（如 25/07/23）
                parts = date_str.split('/')
                if len(parts) == 3:
                    year_part, month_part, day_part = parts

                    # 年份处理：25 表示 2025年
                    year = int(year_part)
                    if year < 50:  # 00-49 认为是20xx年
                        full_year = 2000 + year
                    else:  # 50-99 认为是19xx年
                        full_year = 1900 + year

                    month = int(month_part)
                    day = int(day_part)

                    # 构造标准日期字符串
                    standard_date = f"{full_year:04d}-{month:02d}-{day:02d}"
                else:
                    return None, None
            else:
                # 已经是标准格式
                standard_date = date_str

            # 解析UTC时间
            utc_datetime = datetime.strptime(f"{standard_date} {time_str}", '%Y-%m-%d %H:%M:%S')

            # 转换为北京时间（UTC+8）
            beijing_datetime = utc_datetime + timedelta(hours=8)

            beijing_date = beijing_datetime.strftime('%Y-%m-%d')
            beijing_time = beijing_datetime.strftime('%H:%M:%S')

            return beijing_date, beijing_time

        except Exception as e:
            logger.error(f"时间转换失败: {str(e)}, 输入: {date_str} {time_str}")
            return None, None

    def parse_cc_format(self, file_path, encoding):
        """解析CC B-格式的记录（完全使用PC_BAK.py的方法）
        格式示例: "CC B-30CQ MAR21 122040 ZUGY ZPJH 1807"
        对应: CC 飞机号 月份日期 时间 起飞机场 着陆机场 航班号后四位

        注意：MAR21表示3月21日，年份使用程序执行时的当前年份
        """
        try:
            logger.info(f"    开始CC格式解析，使用编码: {encoding}")

            # CC格式的正则表达式
            # CC B-30CQ MAR21 122040 ZUGY ZPJH 1807
            cc_pattern = re.compile(r'CC\s+(B-[A-Z0-9]{4})\s+([A-Z]{3})(\d{2})\s+(\d{6})\s+([A-Z]{4})\s+([A-Z]{4})\s+(\d{4})', re.I)

            records = []
            line_count = 0

            with open(file_path, 'r', encoding=encoding, errors='ignore') as f:
                for line in f:
                    line_count += 1

                    # 跳过二进制行
                    if self.is_binary_line(line):
                        continue

                    line = line.strip()
                    if not line:
                        continue

                    # 查找CC格式
                    cc_match = cc_pattern.search(line)
                    if cc_match:
                        aircraft, month_abbr, day_str, time_str, dep_airport, arr_airport, flight_suffix = cc_match.groups()

                        # 转换月份
                        month_num = self.convert_month_abbr_to_number(month_abbr)
                        if not month_num:
                            logger.warning(f"    无法识别月份: {month_abbr}")
                            continue

                        # 解析日期：MAR21中的21是日期，不是年份
                        day_int = int(day_str)
                        if day_int < 1 or day_int > 31:
                            logger.warning(f"    无效日期: {day_int}")
                            continue

                        # 解析时间：122040 -> 12:20:40
                        if len(time_str) == 6:
                            hour = time_str[:2]
                            minute = time_str[2:4]
                            second = time_str[4:6]
                            formatted_time = f"{hour}:{minute}:{second}"
                        else:
                            logger.warning(f"    无效时间格式: {time_str}")
                            continue

                        # 使用当前年份
                        current_year = datetime.now().year

                        # 构造日期对象
                        try:
                            date_obj = datetime(current_year, month_num, day_int)

                            # 如果日期大于当前日期，使用前一年
                            if date_obj > datetime.now():
                                date_obj = datetime(current_year - 1, month_num, day_int)

                            # 构造完整的datetime对象
                            time_obj = datetime.strptime(formatted_time, '%H:%M:%S').time()
                            full_datetime = datetime.combine(date_obj.date(), time_obj)

                            record = {
                                'ac': aircraft.upper(),
                                'date': f"{date_obj.strftime('%y')}/{month_num:02d}/{day_int:02d}",
                                'time': formatted_time,
                                'datetime': full_datetime,
                                'line_num': line_count,
                                'raw_line': line,
                                'source': 'CC_format'
                            }

                            records.append(record)
                            logger.info(f"    找到CC记录: {aircraft} {month_abbr}{day_str} {formatted_time}")

                        except ValueError as e:
                            logger.warning(f"    日期时间构造失败: {str(e)}")
                            continue

            logger.info(f"    CC格式解析完成，找到 {len(records)} 条记录")
            return records

        except Exception as e:
            logger.error(f"    CC格式解析异常: {str(e)}")
            return []

    def convert_month_abbr_to_number(self, month_abbr):
        """将月份缩写转换为数字"""
        month_map = {
            'JAN': 1, 'FEB': 2, 'MAR': 3, 'APR': 4, 'MAY': 5, 'JUN': 6,
            'JUL': 7, 'AUG': 8, 'SEP': 9, 'OCT': 10, 'NOV': 11, 'DEC': 12
        }
        return month_map.get(month_abbr.upper())

    def is_valid_data_folder(self, folder_path):
        """判断是否为有效的数据文件夹"""
        required_files = ['DAR.DAT', 'QAR.DAT', 'MSG.DAT']

        for file_name in required_files:
            if os.path.exists(os.path.join(folder_path, file_name)):
                return True

        return False

    def copy_to_data_bak(self, src_folder, aircraft, date_info):
        """复制到DATA_BAK目录（参考PC_BAK.py）"""
        try:
            if not aircraft or not date_info:
                logger.warning(f"飞机号或日期信息缺失: {aircraft}, {date_info}")
                return False

            # 检查目标磁盘空间
            if not self.check_disk_space(ACTUAL_DATA_BAK_PATH, 0.5):  # 至少500MB
                logger.error("DATA_BAK磁盘空间不足")
                return False

            # 构建目标路径: Z:\DATA_BAK\FDIMU_PC\yyyy\aircraft\yyyy-mmdd（参考PC_BAK.py）
            year = date_info[:4]
            month = date_info[4:6]
            day = date_info[6:8]

            # PC_BAK.py的路径格式：year/aircraft/year-mmdd
            target_base = os.path.join(ACTUAL_DATA_BAK_PATH, year, aircraft)
            dst_folder = os.path.join(target_base, f"{year}-{month}{day}")

            # 创建目标目录
            os.makedirs(dst_folder, exist_ok=True)

            # 扫描需要复制的文件和文件夹（参考PC_BAK.py）
            files_to_copy = []
            folders_to_copy = []

            for item in os.listdir(src_folder):
                item_path = os.path.join(src_folder, item)

                if os.path.isfile(item_path):
                    # 检查文件扩展名
                    ext = os.path.splitext(item)[1].upper()
                    if ext in ['.DAT', '.QAR', '.QA2']:
                        files_to_copy.append(item_path)
                elif os.path.isdir(item_path):
                    # 检查文件夹名称
                    if item.upper().endswith(('.REP', '.REC', '.QAR')):
                        folders_to_copy.append(item_path)

            # 复制文件
            for file_path in files_to_copy:

                filename = os.path.basename(file_path)
                target_file = os.path.join(dst_folder, filename)

                try:
                    # 检查目标文件是否已存在
                    if os.path.exists(target_file):
                        logger.info(f"跳过已存在的文件: {filename}")
                        continue

                    shutil.copy2(file_path, target_file)
                    logger.info(f"复制文件: {filename}")
                except Exception as e:
                    logger.error(f"复制文件失败: {file_path} -> {target_file}, 错误: {e}")

            # 复制文件夹
            for folder_path in folders_to_copy:
                folder_name = os.path.basename(folder_path)
                target_subfolder = os.path.join(dst_folder, folder_name)

                try:
                    # 检查目标文件夹是否已存在
                    if os.path.exists(target_subfolder):
                        logger.info(f"跳过已存在的文件夹: {folder_name}")
                        continue

                    shutil.copytree(folder_path, target_subfolder)
                    logger.info(f"复制文件夹: {folder_name}")
                except Exception as e:
                    logger.error(f"复制文件夹失败: {folder_path} -> {target_subfolder}, 错误: {e}")

            logger.info(f"复制到DATA_BAK完成: {dst_folder}")
            return True

        except Exception as e:
            logger.error(f"复制到DATA_BAK失败: {str(e)}")
            self.errors += 1
            return False

    def copy_to_qar_pc(self, src_folder, aircraft, date_info):
        """复制到QAR_PC目录并重命名（第四步：FIM文件复制）"""
        try:
            if not aircraft or not date_info:
                logger.warning(f"飞机号或日期信息缺失: {aircraft}, {date_info}")
                return False

            # 构建新文件夹名: B-xxxx_yyyymmdd0000.pc
            new_folder_name = f"{aircraft}_{date_info}0000.pc"
            dst_folder = os.path.join(ACTUAL_QAR_PC_PATH, new_folder_name)

            # 如果目标文件夹已存在，先删除
            if os.path.exists(dst_folder):
                shutil.rmtree(dst_folder)

            # 直接复制整个文件夹
            shutil.copytree(src_folder, dst_folder)

            logger.info(f"复制到QAR_PC完成: {dst_folder}")
            return True

        except Exception as e:
            logger.error(f"复制到QAR_PC失败: {str(e)}")
            self.errors += 1
            return False

    def check_disk_space(self, path, min_free_gb=1):
        """检查磁盘剩余空间"""
        try:
            if os.name == 'nt':  # Windows
                import ctypes
                free_bytes = ctypes.c_ulonglong(0)
                ctypes.windll.kernel32.GetDiskFreeSpaceExW(
                    ctypes.c_wchar_p(path),
                    ctypes.pointer(free_bytes),
                    None,
                    None
                )
                free_gb = free_bytes.value / (1024**3)
                return free_gb >= min_free_gb
            else:
                statvfs = os.statvfs(path)
                free_gb = (statvfs.f_frsize * statvfs.f_bavail) / (1024**3)
                return free_gb >= min_free_gb
        except Exception as e:
            logger.warning(f"检查磁盘空间失败: {str(e)}")
            return True  # 检查失败时假设空间足够

class PCAutoUnzipProcessor:
    """PC自动解压处理器"""

    def __init__(self, gui_callback=None, date_manager=None):
        self.date_manager = date_manager or DateRangeManager()
        self.file_processor = FileProcessor(gui_callback)
        self.gui_callback = gui_callback

        # 确保必要的目录存在 - 使用实际路径
        os.makedirs(ACTUAL_TEMP_PATH, exist_ok=True)
        os.makedirs(ACTUAL_DATA_BAK_PATH, exist_ok=True)
        os.makedirs(ACTUAL_QAR_PC_PATH, exist_ok=True)
        os.makedirs(QAR_PC_LOG_DIR, exist_ok=True)

    def process_all(self):
        """处理所有符合条件的文件（重构为6步骤）"""
        try:
            self.file_processor.update_gui("开始处理")
            logger.info("开始PC自动解压处理")
            logger.info(f"监控路径: {ACTUAL_PC_MCC_PATH}")
            logger.info(f"临时路径: {ACTUAL_TEMP_PATH}")

            # 检查监控路径是否存在
            if not os.path.exists(ACTUAL_PC_MCC_PATH):
                logger.error(f"监控路径不存在: {ACTUAL_PC_MCC_PATH}")
                self.file_processor.update_gui("错误：监控路径不存在")
                return

            # 第一步：扫描并复制文件
            logger.info("第一步：扫描并复制文件")
            if hasattr(self.file_processor, 'gui_callback'):
                self.file_processor.gui_callback.update_current_step("第一步：扫描并复制文件")
                self.file_processor.gui_callback.switch_to_step(1)
            self.step1_scan_and_copy_files()

            # 第二步：解压压缩文件
            logger.info("第二步：解压压缩文件")
            if hasattr(self.file_processor, 'gui_callback'):
                self.file_processor.gui_callback.update_current_step("第二步：解压压缩文件")
                self.file_processor.gui_callback.switch_to_step(2)
            self.step2_extract_archives()

            # 第三步：文件名和MSG.DAT信息读取
            logger.info("第三步：文件名和MSG.DAT信息读取")
            if hasattr(self.file_processor, 'gui_callback'):
                self.file_processor.gui_callback.update_current_step("第三步：文件名和MSG.DAT信息读取")
                self.file_processor.gui_callback.switch_to_step(3)
            data_info = self.step3_read_data_info()

            # 第四步：FIM文件复制
            logger.info("第四步：FIM文件复制")
            if hasattr(self.file_processor, 'gui_callback'):
                self.file_processor.gui_callback.update_current_step("第四步：FIM文件复制")
                self.file_processor.gui_callback.switch_to_step(4)
            self.step4_copy_to_qar_pc(data_info)

            # 第五步：PC卡数据备份
            logger.info("第五步：PC卡数据备份")
            if hasattr(self.file_processor, 'gui_callback'):
                self.file_processor.gui_callback.update_current_step("第五步：PC卡数据备份")
                self.file_processor.gui_callback.switch_to_step(5)
            self.step5_backup_to_fdimu_pc(data_info)

            # 第六步：文件压缩
            logger.info("第六步：文件压缩")
            if hasattr(self.file_processor, 'gui_callback'):
                self.file_processor.gui_callback.update_current_step("第六步：文件压缩")
                self.file_processor.gui_callback.switch_to_step(6)
            self.step6_create_zip_files(data_info)

            # 清理临时文件
            logger.info("清理临时文件")
            if hasattr(self.file_processor, 'gui_callback'):
                self.file_processor.gui_callback.update_current_step("清理临时文件")
            self.cleanup_temp_folders()

            self.file_processor.update_gui("处理完成")
            if hasattr(self.file_processor, 'gui_callback'):
                self.file_processor.gui_callback.update_current_step("处理完成")
            logger.info("PC自动解压处理完成")

        except Exception as e:
            logger.error(f"处理过程中发生错误: {str(e)}")
            self.file_processor.update_gui(f"处理错误: {str(e)}")

    def step1_scan_and_copy_files(self):
        """第一步：扫描并复制文件"""
        try:
            # 获取当前监控的日期范围
            start_date, end_date = self.date_manager.get_date_range()
            logger.info(f"监控日期范围: {start_date} 到 {end_date}")

            # 生成日期范围内的所有日期
            current_date = start_date
            processed_count = 0

            while current_date <= end_date:
                date_str = current_date.strftime('%Y%m%d')
                year_month = current_date.strftime('%Y-%m')

                # 构建源路径
                source_date_path = os.path.join(ACTUAL_PC_MCC_PATH, year_month, date_str)
                logger.info(f"检查路径: {source_date_path}")

                if not os.path.exists(source_date_path):
                    logger.info(f"路径不存在: {source_date_path}")
                    current_date += timedelta(days=1)
                    continue

                logger.info(f"找到数据路径: {source_date_path}")
                processed_count += 1

                # 构建目标路径
                target_date_path = os.path.join(ACTUAL_TEMP_PATH, date_str)

                # 读取该日期文件夹的日志（从原文件夹位置读取）
                log_file = os.path.join(source_date_path, "QAR_PC.log")
                processed_files = self.file_processor.read_processed_files(log_file)

                # 扫描源目录中的文件和文件夹
                items = os.listdir(source_date_path)
                logger.info(f"源目录 {source_date_path} 中有 {len(items)} 个项目")

                for item in items:
                    if item == "QAR_PC.log":
                        continue

                    item_path = os.path.join(source_date_path, item)

                    # 检查是否已处理过
                    if item in processed_files:
                        logger.info(f"跳过已处理的项目: {item}")
                        continue

                    # 确定文件类型
                    if os.path.isfile(item_path):
                        ext = os.path.splitext(item)[1].upper()
                        if ext == '.ZIP':
                            file_type = 'ZIP'
                        elif ext == '.RAR':
                            file_type = 'RAR'
                        elif ext == '.7Z':
                            file_type = '7Z'
                        else:
                            logger.info(f"跳过不支持的文件类型: {item} ({ext})")
                            continue  # 跳过不支持的文件类型
                    else:
                        file_type = '文件夹'

                    logger.info(f"处理项目: {item} (类型: {file_type})")

                    # 执行复制操作
                    target_item_path = os.path.join(target_date_path, item)
                    success = self.file_processor.copy_file_or_folder(item_path, target_item_path, log_file)

                    # 添加到第一步结果
                    if hasattr(self.file_processor, 'gui_callback'):
                        self.file_processor.gui_callback.add_step_result(1,
                            path=source_date_path,
                            name=item,
                            type=file_type,
                            result='成功' if success else '失败'
                        )

                    # PC卡统计改为在第4步统计，这里不统计
                    pass

                # 移动到下一天
                current_date += timedelta(days=1)

            logger.info(f"第一步完成，处理了 {processed_count} 个日期文件夹")

        except Exception as e:
            logger.error(f"第一步处理失败: {str(e)}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")

    def step2_extract_archives(self):
        """第二步：解压压缩文件"""
        try:
            # 遍历临时目录中的所有压缩文件
            for root, dirs, files in os.walk(ACTUAL_TEMP_PATH):
                for file in files:
                    if file.lower().endswith(('.zip', '.rar', '.7z')):
                        archive_path = os.path.join(root, file)

                        # 确定文件类型
                        ext = os.path.splitext(file)[1].upper()
                        if ext == '.ZIP':
                            file_type = 'ZIP'
                        elif ext == '.RAR':
                            file_type = 'RAR'
                        elif ext == '.7Z':
                            file_type = '7Z'

                        # 解压目录（与压缩文件同名，去掉扩展名）
                        extract_dir = os.path.join(root, os.path.splitext(file)[0])

                        # 执行解压
                        success = self.file_processor.extract_archive(archive_path, extract_dir)

                        # 添加到第二步结果
                        if hasattr(self.file_processor, 'gui_callback'):
                            self.file_processor.gui_callback.add_step_result(2,
                                path=root,
                                name=file,
                                type=file_type,
                                result='成功' if success else '失败'
                            )

        except Exception as e:
            logger.error(f"第二步处理失败: {str(e)}")

    def step3_read_data_info(self):
        """第三步：文件名和MSG.DAT信息读取"""
        data_info = []
        try:
            # 遍历临时目录中的所有数据文件夹
            for root, dirs, files in os.walk(ACTUAL_TEMP_PATH):
                for dir_name in dirs:
                    folder_path = os.path.join(root, dir_name)

                    # 检查是否是有效的数据文件夹
                    if not self.file_processor.is_valid_data_folder(folder_path):
                        continue

                    # 从文件夹名提取飞机号，从文件夹路径或其父目录提取日期信息
                    mcc_aircraft = self.file_processor.extract_aircraft_from_folder(dir_name)
                    mcc_date = self.file_processor.extract_date_with_parent_folders(folder_path)

                    # 从MSG.DAT文件提取信息
                    msg_aircraft, msg_date = self.file_processor.extract_from_msg_dat(folder_path)

                    # 确定最终的飞机号和日期（BAK）
                    bak_aircraft, bak_date = self.file_processor.extract_aircraft_and_date(dir_name, folder_path)

                    # 判断结果
                    if bak_aircraft and bak_date:
                        result = '成功'
                    else:
                        result = '失败'

                    # 存储数据信息
                    folder_info = {
                        'folder_name': dir_name,
                        'folder_path': folder_path,
                        'mcc_aircraft': mcc_aircraft or '',
                        'mcc_date': mcc_date or '',
                        'msg_aircraft': msg_aircraft or '',
                        'msg_date': msg_date or '',
                        'bak_aircraft': bak_aircraft or '',
                        'bak_date': bak_date or '',
                        'result': result
                    }
                    data_info.append(folder_info)

                    # 添加到第三步结果
                    if hasattr(self.file_processor, 'gui_callback'):
                        self.file_processor.gui_callback.add_step_result(3,
                            data_name=dir_name,
                            mcc_aircraft=mcc_aircraft or '',
                            mcc_date=mcc_date or '',
                            msg_aircraft=msg_aircraft or '',
                            msg_date=msg_date or '',
                            bak_aircraft=bak_aircraft or '',
                            bak_date=bak_date or '',
                            result=result
                        )

                    logger.info(f"第三步处理: {dir_name} - 飞机号:{bak_aircraft}, 日期:{bak_date}, 结果:{result}")

        except Exception as e:
            logger.error(f"第三步处理失败: {str(e)}")

        return data_info

    def step4_copy_to_qar_pc(self, data_info):
        """第四步：FIM文件复制"""
        try:
            for info in data_info:
                if info['result'] != '成功':
                    # 跳过失败的数据
                    if hasattr(self.file_processor, 'gui_callback'):
                        self.file_processor.gui_callback.add_step_result(4,
                            data_name=info['folder_name'],
                            mcc_aircraft=info['mcc_aircraft'],
                            mcc_date=info['mcc_date'],
                            msg_aircraft=info['msg_aircraft'],
                            msg_date=info['msg_date'],
                            bak_aircraft=info['bak_aircraft'],
                            bak_date=info['bak_date'],
                            result='跳过'
                        )
                    continue

                # 执行复制到QAR_PC
                success = self.file_processor.copy_to_qar_pc(
                    info['folder_path'],
                    info['bak_aircraft'],
                    info['bak_date']
                )

                # 添加到第四步结果
                if hasattr(self.file_processor, 'gui_callback'):
                    self.file_processor.gui_callback.add_step_result(4,
                        data_name=info['folder_name'],
                        mcc_aircraft=info['mcc_aircraft'],
                        mcc_date=info['mcc_date'],
                        msg_aircraft=info['msg_aircraft'],
                        msg_date=info['msg_date'],
                        bak_aircraft=info['bak_aircraft'],
                        bak_date=info['bak_date'],
                        result='成功' if success else '失败'
                    )

                # 更新成功状态
                info['qar_pc_success'] = success

                # 第4步PC卡统计：每个成功复制到QAR_PC的文件夹算一个PC卡
                if success:
                    self.file_processor.processed_pc_cards += 1
                    logger.info(f"PC卡统计+1，当前总数: {self.file_processor.processed_pc_cards}")

                logger.info(f"第四步处理: {info['folder_name']} - 复制到QAR_PC: {'成功' if success else '失败'}")

        except Exception as e:
            logger.error(f"第四步处理失败: {str(e)}")

    def step5_backup_to_fdimu_pc(self, data_info):
        """第五步：PC卡数据备份"""
        try:
            for info in data_info:
                if info['result'] != '成功' or not info.get('qar_pc_success', False):
                    # 跳过失败的数据或QAR_PC复制失败的数据
                    if hasattr(self.file_processor, 'gui_callback'):
                        self.file_processor.gui_callback.add_step_result(5,
                            data_name=info['folder_name'],
                            mcc_aircraft=info['mcc_aircraft'],
                            mcc_date=info['mcc_date'],
                            msg_aircraft=info['msg_aircraft'],
                            msg_date=info['msg_date'],
                            bak_aircraft=info['bak_aircraft'],
                            bak_date=info['bak_date'],
                            result='跳过'
                        )
                    continue

                # 从QAR_PC复制到FDIMU_PC
                qar_pc_folder = f"{info['bak_aircraft']}_{info['bak_date']}0000.pc"
                qar_pc_path = os.path.join(ACTUAL_QAR_PC_PATH, qar_pc_folder)

                success = self.file_processor.copy_to_data_bak(
                    qar_pc_path,
                    info['bak_aircraft'],
                    info['bak_date']
                )

                # 添加到第五步结果
                if hasattr(self.file_processor, 'gui_callback'):
                    self.file_processor.gui_callback.add_step_result(5,
                        data_name=info['folder_name'],
                        mcc_aircraft=info['mcc_aircraft'],
                        mcc_date=info['mcc_date'],
                        msg_aircraft=info['msg_aircraft'],
                        msg_date=info['msg_date'],
                        bak_aircraft=info['bak_aircraft'],
                        bak_date=info['bak_date'],
                        result='成功' if success else '失败'
                    )

                # 更新PC卡统计（每个成功处理的文件夹算一个PC卡）
                if success:
                    # 注意：这里不再重复计算，因为在第一步已经计算过了
                    pass

                logger.info(f"第五步处理: {info['folder_name']} - 备份到FDIMU_PC: {'成功' if success else '失败'}")

        except Exception as e:
            logger.error(f"第五步处理失败: {str(e)}")

    def scan_and_copy_files(self):
        """扫描并复制文件到temp_path"""
        self.file_processor.update_gui("扫描文件中")

        # 获取需要扫描的年月目录
        year_months = self.date_manager.get_year_months_in_range()

        logger.info(f"需要扫描的年月目录: {year_months}")

        for year_month in year_months:
            year_month_path = os.path.join(ACTUAL_PC_MCC_PATH, year_month)
            logger.info(f"检查年月目录: {year_month_path}")

            if not os.path.exists(year_month_path):
                logger.info(f"年月目录不存在: {year_month_path}")
                continue

            logger.info(f"扫描年月目录: {year_month_path}")
            self.process_year_month_folder(year_month_path)

    def process_year_month_folder(self, year_month_path):
        """处理年月文件夹"""
        try:
            for item in os.listdir(year_month_path):
                item_path = os.path.join(year_month_path, item)

                if os.path.isdir(item_path) and len(item) == 8 and item.isdigit():
                    # 检查是否在日期范围内
                    if self.date_manager.is_date_in_range(item):
                        self.process_date_folder(item_path, item)

        except Exception as e:
            logger.error(f"处理年月文件夹失败: {str(e)}")

    def process_date_folder(self, date_folder_path, date_str):
        """处理日期文件夹"""
        try:
            logger.info(f"开始处理日期文件夹: {date_folder_path}")
            self.file_processor.update_gui(f"处理日期: {date_str}")

            log_file = os.path.join(date_folder_path, "QAR_PC.log")
            logger.info(f"检查日志文件: {log_file}")

            # 读取已处理的文件列表
            processed_files = self.file_processor.read_processed_files(log_file)
            logger.info(f"已处理文件数量: {len(processed_files)}")

            # 获取当前目录中的所有文件和文件夹
            current_items = set()
            for item in os.listdir(date_folder_path):
                if item != "QAR_PC.log":  # 排除日志文件本身
                    current_items.add(item)

            logger.info(f"当前目录文件数量: {len(current_items)}")
            logger.info(f"当前目录文件列表: {list(current_items)[:5]}...")  # 只显示前5个

            # 找出需要复制的文件（检查全局和本地日志）
            items_to_copy = []
            main_log = os.path.join(QAR_PC_LOG_DIR, 'QAR_PC.log')

            for item in current_items:
                # 检查本地日志和全局日志
                if (not self.file_processor.is_file_processed(item, log_file) and
                    not self.file_processor.is_file_processed(item, main_log)):
                    items_to_copy.append(item)
                else:
                    logger.info(f"文件已处理过，跳过: {item}")

            if not items_to_copy:
                logger.info(f"日期文件夹 {date_str} 中没有新文件需要处理")
                return

            # 复制新文件到temp_path
            temp_date_path = os.path.join(ACTUAL_TEMP_PATH, date_str)
            logger.info(f"创建临时目录: {temp_date_path}")
            os.makedirs(temp_date_path, exist_ok=True)

            logger.info(f"开始复制 {len(items_to_copy)} 个文件/文件夹")
            for item in items_to_copy:
                src_path = os.path.join(date_folder_path, item)
                dst_path = os.path.join(temp_date_path, item)
                logger.info(f"复制: {src_path} -> {dst_path}")

                self.file_processor.copy_file_or_folder(src_path, dst_path, log_file)

            logger.info(f"日期文件夹 {date_str} 处理完成，复制了 {len(items_to_copy)} 个项目")

        except Exception as e:
            logger.error(f"处理日期文件夹失败: {str(e)}")

    def extract_archives_in_temp(self):
        """解压temp_path中的所有压缩文件"""
        self.file_processor.update_gui("解压压缩文件")

        try:
            for root, dirs, files in os.walk(ACTUAL_TEMP_PATH):
                for file in files:
                    file_path = os.path.join(root, file)
                    file_ext = os.path.splitext(file)[1].lower()

                    if file_ext in ['.zip', '.rar', '.7z']:
                        logger.info(f"解压文件: {file_path}")

                        # 创建解压目录（使用文件名作为目录名）
                        extract_dir = os.path.join(root, os.path.splitext(file)[0])
                        os.makedirs(extract_dir, exist_ok=True)

                        self.file_processor.extract_archive(file_path, extract_dir)

        except Exception as e:
            logger.error(f"解压压缩文件失败: {str(e)}")

    def process_data_folders(self):
        """处理数据文件夹"""
        self.file_processor.update_gui("处理数据文件夹")

        try:
            for root, dirs, files in os.walk(ACTUAL_TEMP_PATH):
                if self.file_processor.is_valid_data_folder(root):
                    folder_name = os.path.basename(root)

                    # 提取飞机号和日期信息
                    aircraft, date_info = self.file_processor.extract_aircraft_and_date(folder_name, root)

                    if aircraft and date_info:
                        logger.info(f"处理数据文件夹: {folder_name}, 飞机号: {aircraft}, 日期: {date_info}")

                        # 获取详细信息（参考PC_BAK.py）
                        mcc_aircraft = self.file_processor.extract_aircraft_from_folder(folder_name)
                        mcc_date = self.file_processor.extract_date_from_folder(folder_name)
                        msg_aircraft, msg_date = self.file_processor.extract_from_msg_dat(root)
                        bak_date = date_info  # 最终使用的日期

                        # 添加到处理结果表格
                        if hasattr(self.file_processor.gui_callback, 'add_processing_result'):
                            self.file_processor.gui_callback.add_processing_result(
                                data_name=folder_name,
                                mcc_aircraft=mcc_aircraft or '',
                                mcc_date=mcc_date or '',
                                msg_aircraft=msg_aircraft or '',
                                msg_date=msg_date or '',
                                bak_aircraft=aircraft,
                                bak_date=bak_date,
                                result='处理中'
                            )

                        # 复制到DATA_BAK
                        data_bak_success = self.file_processor.copy_to_data_bak(root, aircraft, date_info)

                        # 复制到QAR_PC
                        qar_pc_success = self.file_processor.copy_to_qar_pc(root, aircraft, date_info)

                        # 检查复制是否都成功
                        if data_bak_success and qar_pc_success:
                            # 数据文件夹处理成功（PC卡统计在复制阶段已经计算）
                            pass

                            # 更新处理结果为完成
                            if hasattr(self.file_processor.gui_callback, 'update_processing_result'):
                                self.file_processor.gui_callback.update_processing_result(
                                    folder_name,
                                    result='成功'
                                )

                            logger.info(f"数据文件夹处理成功: {folder_name}")
                        else:
                            # 复制失败，更新状态
                            if hasattr(self.file_processor.gui_callback, 'update_processing_result'):
                                self.file_processor.gui_callback.update_processing_result(
                                    folder_name,
                                    result='复制失败'
                                )

                            logger.error(f"数据文件夹处理失败: {folder_name}")
                    else:
                        logger.warning(f"无法提取飞机号或日期信息: {folder_name}")

        except Exception as e:
            logger.error(f"处理数据文件夹失败: {str(e)}")

    def create_zip_file(self, folder_path, aircraft, date_info):
        """将文件夹中的所有文件压缩成ZIP文件
        参数:
            folder_path: 包含要压缩文件的文件夹路径
            aircraft: 飞机号（格式为B-xxxx）
            date_info: 日期信息（格式为yyyymmdd）
        返回:
            压缩是否成功
        """
        try:
            # 目标目录 - 优先使用测试路径，如果没有设置则使用默认路径
            target_dir = getattr(self, 'test_zip_dir', r"E:\ZIP")
            os.makedirs(target_dir, exist_ok=True)

            # 压缩文件名格式: B-xxxx_yyyymmdd000000.zip
            zip_filename = f"{aircraft}_{date_info}000000.zip"
            zip_filepath = os.path.join(target_dir, zip_filename)

            logger.info(f"开始创建压缩文件: {zip_filepath}")

            # 创建ZIP文件
            with zipfile.ZipFile(zip_filepath, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # 遍历文件夹中的所有文件
                for root, dirs, files in os.walk(folder_path):
                    for file in files:
                        file_path = os.path.join(root, file)
                        # 将文件添加到ZIP中，使用相对路径
                        zipf.write(file_path, os.path.basename(file_path))

            logger.info(f"压缩文件创建成功: {zip_filepath}")
            return True

        except Exception as e:
            logger.error(f"创建压缩文件失败: {str(e)}")
            self.errors += 1
            return False

    def step6_create_zip_files(self, data_info):
        """第六步：文件压缩"""
        try:
            logger.info(f"开始第六步文件压缩")
            
            # 检查data_info是否为空
            if not data_info:
                logger.warning("data_info为空，没有可压缩的数据")
                return
            
            logger.info(f"共 {len(data_info)} 个数据项")
            
            # 输出data_info的结构
            logger.info(f"data_info第一项结构: {list(data_info[0].keys())} 若data_info为空则不显示")
            
            for info in data_info:
                logger.info(f"处理数据项: {info.get('folder_name', '未知')}, 结果: {info.get('result', '未知')}, QAR_PC复制成功: {info.get('qar_pc_success', False)}")
                
                # 检查必要字段是否存在
                required_fields = ['folder_name', 'result', 'bak_aircraft', 'bak_date']
                missing_fields = [field for field in required_fields if field not in info]
                if missing_fields:
                    logger.warning(f"数据项缺少必要字段: {missing_fields}, 跳过")
                    continue
                
                if info['result'] != '成功' or not info.get('qar_pc_success', False):
                    # 跳过失败的数据或QAR_PC复制失败的数据
                    if hasattr(self, 'file_processor') and hasattr(self.file_processor, 'gui_callback') and self.file_processor.gui_callback is not None:
                        self.file_processor.gui_callback.add_step_result(6,
                            data_name=info['folder_name'],
                            mcc_aircraft=info.get('mcc_aircraft', ''),
                            mcc_date=info.get('mcc_date', ''),
                            msg_aircraft=info.get('msg_aircraft', ''),
                            msg_date=info.get('msg_date', ''),
                            bak_aircraft=info['bak_aircraft'],
                            bak_date=info['bak_date'],
                            result='跳过'
                        )
                    logger.warning(f"跳过数据项: {info['folder_name']}，原因: 处理结果={'成功' if info['result'] == '成功' else '失败'}，QAR_PC复制={'成功' if info.get('qar_pc_success', False) else '失败'}")
                    continue

                # 获取QAR_PC中的文件夹路径
                qar_pc_folder = f"{info['bak_aircraft']}_{info['bak_date']}0000.pc"
                qar_pc_path = os.path.join(ACTUAL_QAR_PC_PATH, qar_pc_folder)
                logger.info(f"QAR_PC文件夹路径: {qar_pc_path}")

                # 检查QAR_PC文件夹是否存在
                if not os.path.exists(qar_pc_path):
                    logger.error(f"QAR_PC文件夹不存在: {qar_pc_path}")
                    if hasattr(self, 'file_processor') and hasattr(self.file_processor, 'gui_callback') and self.file_processor.gui_callback is not None:
                        self.file_processor.gui_callback.add_step_result(6,
                            data_name=info['folder_name'],
                            mcc_aircraft=info.get('mcc_aircraft', ''),
                            mcc_date=info.get('mcc_date', ''),
                            msg_aircraft=info.get('msg_aircraft', ''),
                            msg_date=info.get('msg_date', ''),
                            bak_aircraft=info['bak_aircraft'],
                            bak_date=info['bak_date'],
                            result='失败'
                        )
                    continue

                # 执行压缩
                success = self.create_zip_file(
                    qar_pc_path,
                    info['bak_aircraft'],
                    info['bak_date']
                )

                # 添加到第六步结果
                if hasattr(self, 'file_processor') and hasattr(self.file_processor, 'gui_callback') and self.file_processor.gui_callback is not None:
                    self.file_processor.gui_callback.add_step_result(6,
                        data_name=info['folder_name'],
                        mcc_aircraft=info.get('mcc_aircraft', ''),
                        mcc_date=info.get('mcc_date', ''),
                        msg_aircraft=info.get('msg_aircraft', ''),
                        msg_date=info.get('msg_date', ''),
                        bak_aircraft=info['bak_aircraft'],
                        bak_date=info['bak_date'],
                        result='成功' if success else '失败'
                    )

                logger.info(f"第六步处理: {info['folder_name']} - 创建压缩文件: {'成功' if success else '失败'}")

        except Exception as e:
            logger.error(f"第六步处理失败: {str(e)}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")

    def cleanup_temp_folders(self):
        """清理临时文件夹"""
        try:
            if not os.path.exists(ACTUAL_TEMP_PATH):
                return

            # 获取当前监控的日期范围
            date_range = self.date_manager.get_date_range()

            # 遍历临时目录中的所有日期文件夹
            for item in os.listdir(ACTUAL_TEMP_PATH):
                item_path = os.path.join(ACTUAL_TEMP_PATH, item)

                if os.path.isdir(item_path) and len(item) == 8 and item.isdigit():
                    # 这是一个日期文件夹
                    try:
                        date_obj = datetime.strptime(item, '%Y%m%d')

                        # 检查是否在监控范围内
                        if date_range[0] <= date_obj.date() <= date_range[1]:
                            logger.info(f"检查日期文件夹是否可清理: {item}")
                            self.cleanup_processed_data(item_path, item)
                        else:
                            logger.info(f"日期文件夹超出监控范围，跳过清理: {item}")

                    except ValueError:
                        logger.warning(f"无效的日期文件夹格式: {item}")

        except Exception as e:
            logger.error(f"清理临时文件夹失败: {str(e)}")

    def cleanup_processed_data(self, temp_date_path, date_str):
        """清理已处理完成的数据"""
        try:
            if not os.path.exists(temp_date_path):
                return

            # 检查该日期的所有数据是否都处理成功
            all_success = True
            failed_items = []

            # 遍历临时目录中的所有文件和文件夹
            for item in os.listdir(temp_date_path):
                if item == "QAR_PC.log":
                    continue

                item_path = os.path.join(temp_date_path, item)

                # 检查是否为数据文件夹（包含DAR.DAT等）
                if os.path.isdir(item_path):
                    if self.file_processor.is_valid_data_folder(item_path):
                        # 检查数据文件夹是否处理成功
                        aircraft, date_info = self.file_processor.extract_aircraft_and_date(item, item_path)
                        if not aircraft or not date_info:
                            logger.warning(f"数据文件夹处理失败，保留: {item}")
                            all_success = False
                            failed_items.append(item)
                            continue

                        # 检查是否已复制到目标目录
                        qar_pc_folder = f"{aircraft}_{date_info}0000.pc"
                        qar_pc_path = os.path.join(ACTUAL_QAR_PC_PATH, qar_pc_folder)

                        if not os.path.exists(qar_pc_path):
                            logger.warning(f"QAR_PC目录不存在，数据文件夹处理失败，保留: {item}")
                            all_success = False
                            failed_items.append(item)

                # 压缩文件应该已经被解压并删除，如果还存在说明解压失败
                elif item.lower().endswith(('.zip', '.rar', '.7z')):
                    logger.warning(f"压缩文件未被删除，解压可能失败，保留: {item}")
                    all_success = False
                    failed_items.append(item)

            if all_success:
                # 所有数据都处理成功，删除整个临时日期文件夹
                shutil.rmtree(temp_date_path)
                logger.info(f"所有数据处理成功，删除临时日期文件夹: {temp_date_path}")
            else:
                # 有数据处理失败，只删除成功处理的数据，保留失败的
                for item in os.listdir(temp_date_path):
                    if item not in failed_items and item != "QAR_PC.log":
                        item_path = os.path.join(temp_date_path, item)
                        try:
                            if os.path.isdir(item_path):
                                shutil.rmtree(item_path)
                            else:
                                os.remove(item_path)
                            logger.info(f"删除成功处理的数据: {item}")
                        except Exception as e:
                            logger.error(f"删除数据失败: {item}, 错误: {str(e)}")

                logger.info(f"部分数据处理失败，保留失败数据: {failed_items}")

        except Exception as e:
            logger.error(f"清理已处理数据失败: {str(e)}")

class PCAutoUnzipGUI:
    """PC自动解压GUI界面"""

    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.setup_variables()
        self.setup_styles()

        # 路径配置变量 - 使用实际路径
        self.pc_mcc_path_var = tk.StringVar(value=ACTUAL_PC_MCC_PATH)
        self.temp_path_var = tk.StringVar(value=ACTUAL_TEMP_PATH)
        self.data_bak_path_var = tk.StringVar(value=ACTUAL_DATA_BAK_PATH)
        self.qar_pc_path_var = tk.StringVar(value=ACTUAL_QAR_PC_PATH)

        # 绑定路径变更事件
        self.pc_mcc_path_var.trace_add("write", self.update_global_paths)
        self.temp_path_var.trace_add("write", self.update_global_paths)
        self.data_bak_path_var.trace_add("write", self.update_global_paths)
        self.qar_pc_path_var.trace_add("write", self.update_global_paths)

        # 日期管理器和处理器
        self.date_manager = DateRangeManager(scan_days=7)  # 默认7天
        self.processor = PCAutoUnzipProcessor(gui_callback=self, date_manager=self.date_manager)

        # 更新PC卡计数显示
        self.processed_pc_cards.set(self.processor.file_processor.processed_pc_cards)
        print(f"[DEBUG] 界面PC卡计数已更新为: {self.processor.file_processor.processed_pc_cards}")
        logger.info(f"界面PC卡计数已更新为: {self.processor.file_processor.processed_pc_cards}")

        self.create_widgets()

        # 监控相关 - 已禁用文件系统监控
        self.monitoring_active = False
        self.observer = None  # 不再使用文件系统监控
        self.is_paused = True  # 默认为暂停状态
        self.scan_timer = None  # 定时扫描器

        # 绑定窗口事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 启动状态更新线程
        self.update_thread = threading.Thread(target=self.update_loop, daemon=True)
        self.update_thread.start()

        # 不自动启动监控，等待用户点击开始

    def setup_window(self):
        """设置窗口属性"""
        self.root.title("PC_AUTO_BAK - 文件解压处理工具")
        self.root.geometry("950x850")
        self.root.minsize(800, 700)

        # 设置图标
        try:
            if os.path.exists("app.ico"):
                self.root.iconbitmap("app.ico")
        except:
            pass

        self.root.configure(bg='#f0f2f5')

    def setup_variables(self):
        """设置变量"""
        self.current_task = tk.StringVar(value="正在启动...")
        self.uptime = tk.StringVar(value="00:00:00")
        self.processed_pc_cards = tk.IntVar(value=0)
        self.error_count = tk.IntVar(value=0)
        self.date_range_text = tk.StringVar(value="计算中...")
        self.monitoring_status = tk.StringVar(value="已暂停")

        self.start_time = datetime.now()

    def setup_styles(self):
        """设置样式"""
        style = ttk.Style()
        style.theme_use('clam')

        style.configure('Title.TLabel', font=('Microsoft YaHei', 16, 'bold'),
                       background='#f0f2f5', foreground='#2c3e50')
        style.configure('Header.TLabel', font=('Microsoft YaHei', 12, 'bold'),
                       background='#f0f2f5', foreground='#34495e')
        style.configure('Value.TLabel', font=('Microsoft YaHei', 11, 'bold'),
                       background='#f0f2f5', foreground='#2980b9')
        style.configure('Action.TButton', font=('Microsoft YaHei', 10, 'bold'),
                       padding=(20, 10))

    def create_widgets(self):
        """创建界面组件"""
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题区域
        self.create_header(main_frame)
        # 配置信息区域
        self.create_config_section(main_frame)
        # 状态卡片区域
        self.create_status_cards(main_frame)
        # 控制按钮区域
        self.create_control_buttons(main_frame)
        # 详细信息区域
        self.create_details_section(main_frame)

    def create_header(self, parent):
        """创建标题区域"""
        header_frame = ttk.Frame(parent)
        header_frame.pack(fill=tk.X, pady=(0, 10))

        title_label = ttk.Label(header_frame, text="📦 PC AUTO BAK",
                               style='Title.TLabel')
        title_label.pack(side=tk.LEFT)

        self.status_indicator = ttk.Label(header_frame, text="●",
                                         font=('Microsoft YaHei', 20),
                                         foreground='#e74c3c')
        self.status_indicator.pack(side=tk.RIGHT, padx=(10, 0))

        self.status_text = ttk.Label(header_frame, textvariable=self.monitoring_status,
                                    style='Header.TLabel')
        self.status_text.pack(side=tk.RIGHT)

    def create_config_section(self, parent):
        """创建配置信息区域"""
        # 路径配置部分（独立框）
        path_frame = ttk.LabelFrame(parent, text="路径设置", padding="10")
        path_frame.pack(fill=tk.X, pady=(0, 10))

        # 配置网格布局
        for i in range(4):
            path_frame.columnconfigure(i, weight=1)

        # 监控路径
        ttk.Label(path_frame, text="监控路径:",
               font=('Microsoft YaHei', 10, 'bold'),
               foreground='#34495e').grid(row=0, column=0, sticky=tk.W, padx=(0, 10), pady=5)

        ttk.Entry(path_frame, textvariable=self.pc_mcc_path_var,
                font=('Consolas', 10),
                width=40).grid(row=0, column=1, sticky=tk.EW, padx=(0, 10), pady=8)

        # 临时路径
        ttk.Label(path_frame, text="临时路径:",
               font=('Microsoft YaHei', 10, 'bold'),
               foreground='#34495e').grid(row=0, column=2, sticky=tk.W, padx=(0, 10), pady=8)

        ttk.Entry(path_frame, textvariable=self.temp_path_var,
                font=('Consolas', 10),
                width=40).grid(row=0, column=3, sticky=tk.EW, padx=(0, 10), pady=8)

        # DATA_BAK路径
        ttk.Label(path_frame, text="DATA_BAK路径:",
               font=('Microsoft YaHei', 10, 'bold'),
               foreground='#34495e').grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=8)

        ttk.Entry(path_frame, textvariable=self.data_bak_path_var,
                font=('Consolas', 10),
                width=40).grid(row=1, column=1, sticky=tk.EW, padx=(0, 10), pady=8)

        # QAR_PC路径
        ttk.Label(path_frame, text="QAR_PC路径:",
               font=('Microsoft YaHei', 10, 'bold'),
               foreground='#34495e').grid(row=1, column=2, sticky=tk.W, padx=(0, 10), pady=8)

        ttk.Entry(path_frame, textvariable=self.qar_pc_path_var,
                font=('Consolas', 10),
                width=40).grid(row=1, column=3, sticky=tk.EW, padx=(0, 10), pady=8)

        # 删除了保存和应用按钮，路径将在启动时直接使用

        # 日期设置部分（独立框）
        date_frame = ttk.LabelFrame(parent, text="日期设置", padding="10")
        date_frame.pack(fill=tk.X, pady=(0, 10))

        # 日期范围和扫描天数信息
        date_content_frame = ttk.Frame(date_frame)
        date_content_frame.pack(fill=tk.X, pady=2)

        ttk.Label(date_content_frame, text="📅 当前监控日期范围:",
                 font=('Microsoft YaHei', 11, 'bold'),
                 foreground='#e67e22').pack(side=tk.LEFT, padx=(0, 15))

        date_value_label = ttk.Label(date_content_frame, textvariable=self.date_range_text,
                                    font=('Consolas', 11, 'bold'),
                                    foreground='#e74c3c',
                                    background='#fdf2e9',
                                    relief='solid',
                                    borderwidth=1,
                                    padding=(12, 6))
        date_value_label.pack(side=tk.LEFT, padx=(0, 20))

        # 扫描天数设置
        ttk.Label(date_content_frame, text="🔍 扫描天数:",
                 font=('Microsoft YaHei', 11, 'bold'),
                 foreground='#27ae60').pack(side=tk.LEFT, padx=(0, 10))

        self.scan_days_var = tk.StringVar(value=str(self.date_manager.scan_days))
        scan_days_entry = ttk.Entry(date_content_frame, textvariable=self.scan_days_var,
                                   width=5,
                                   font=('Consolas', 11, 'bold'),
                                   justify='center')
        scan_days_entry.pack(side=tk.LEFT, padx=(0, 5))

        ttk.Label(date_content_frame, text="天",
                 font=('Microsoft YaHei', 11, 'bold'),
                 foreground='#27ae60').pack(side=tk.LEFT)

        # 设置按钮
        set_days_btn = ttk.Button(date_content_frame, text="设置",
                                 command=self.set_scan_days)
        set_days_btn.pack(side=tk.LEFT, padx=(10, 0))



    def create_status_cards(self, parent):
        """创建状态卡片"""
        cards_frame = ttk.Frame(parent)
        cards_frame.pack(fill=tk.X, pady=(0, 10))

        for i in range(3):
            cards_frame.columnconfigure(i, weight=1)

        # 运行时间卡片
        self.create_card(cards_frame, "⏱️ 运行时间", self.uptime, 0, 0)

        # 处理统计卡片
        self.stats_card = self.create_card(cards_frame, "📊 PC卡统计", self.processed_pc_cards, 0, 1)

        # 错误统计卡片
        self.create_card(cards_frame, "⚠️ 错误统计", self.error_count, 0, 2)

        # 当前处理步骤卡片（大字体显眼显示）
        self.current_step_card = self.create_step_card(cards_frame, "🔄 当前处理步骤", 1, 0, columnspan=3)

    def create_card(self, parent, title, value_var, row, col, columnspan=1):
        """创建单个状态卡片"""
        card_frame = ttk.LabelFrame(parent, text=title, padding="10")
        card_frame.grid(row=row, column=col, columnspan=columnspan,
                       sticky="ew", padx=5, pady=5)

        if isinstance(value_var, (tk.StringVar, tk.IntVar)):
            value_label = ttk.Label(card_frame, textvariable=value_var,
                                   style='Value.TLabel')
            value_label.pack()
        elif value_var:
            value_label = ttk.Label(card_frame, text=str(value_var),
                                   style='Value.TLabel')
            value_label.pack()

        return card_frame

    def create_step_card(self, parent, title, row, col, columnspan=1):
        """创建当前处理步骤卡片（大字体显眼显示）"""
        card_frame = ttk.LabelFrame(parent, text=title, padding="10")
        card_frame.grid(row=row, column=col, columnspan=columnspan,
                       sticky="ew", padx=5, pady=5)

        # 当前步骤文本（大字体，显眼颜色）
        self.current_step_text = tk.StringVar(value="等待开始")
        step_label = tk.Label(card_frame, textvariable=self.current_step_text,
                             font=('微软雅黑', 16, 'bold'),
                             fg='#e74c3c', bg='#f8f9fa',
                             relief='solid', borderwidth=2,
                             padx=15, pady=5)
        step_label.pack(fill=tk.X)

        return card_frame

    def update_current_step(self, step_text):
        """更新当前处理步骤显示"""
        if hasattr(self, 'current_step_text'):
            self.current_step_text.set(step_text)

    def create_control_buttons(self, parent):
        """创建控制按钮"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(0, 10))

        self.start_btn = ttk.Button(button_frame, text="▶️ 开始",
                                   command=self.start_monitoring_manual,
                                   style='Action.TButton')
        self.start_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.pause_btn = ttk.Button(button_frame, text="⏸️ 暂停",
                                   command=self.pause_monitoring,
                                   style='Action.TButton',
                                   state='disabled')
        self.pause_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.manual_btn = ttk.Button(button_frame, text="🔄 手动处理",
                                    command=self.manual_process,
                                    style='Action.TButton')
        self.manual_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.log_btn = ttk.Button(button_frame, text="📋 查看日志",
                                 command=self.view_logs,
                                 style='Action.TButton')
        self.log_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.monitor_btn = ttk.Button(button_frame, text="📁 打开监控目录",
                                     command=self.open_monitor_folder,
                                     style='Action.TButton')
        self.monitor_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.exit_btn = ttk.Button(button_frame, text="❌ 退出程序",
                                  command=self.on_closing,
                                  style='Action.TButton')
        self.exit_btn.pack(side=tk.RIGHT)

    def create_details_section(self, parent):
        """创建处理结果显示区域"""
        details_frame = ttk.LabelFrame(parent, text="📈 处理结果", padding="15")
        details_frame.pack(fill=tk.BOTH, expand=True)
        details_frame.columnconfigure(0, weight=1)
        details_frame.rowconfigure(0, weight=1)

        # 当前步骤和表格配置
        self.current_step = 1
        self.step_configs = {
            1: {  # 第一步：扫描并复制文件
                'title': '第一步：扫描并复制文件',
                'columns': ('seq', 'path', 'name', 'type', 'result'),
                'column_config': {
                    'seq': ('序号', 60),
                    'path': ('路径', 200),
                    'name': ('文件/文件夹名', 150),
                    'type': ('类型', 80),
                    'result': ('结果', 80)
                }
            },
            2: {  # 第二步：解压压缩文件
                'title': '第二步：解压压缩文件',
                'columns': ('seq', 'path', 'name', 'type', 'result'),
                'column_config': {
                    'seq': ('序号', 60),
                    'path': ('路径', 200),
                    'name': ('文件名', 150),
                    'type': ('类型', 80),
                    'result': ('结果', 80)
                }
            },
            3: {  # 第三步：文件名和MSG.DAT信息读取
                'title': '第三步：文件名和MSG.DAT信息读取',
                'columns': ('seq', 'data_name', 'mcc_aircraft', 'mcc_date', 'msg_aircraft', 'msg_date', 'bak_aircraft', 'bak_date', 'result'),
                'column_config': {
                    'seq': ('序号', 60),
                    'data_name': ('PC卡数据', 120),
                    'mcc_aircraft': ('飞机号_MCC', 80),
                    'mcc_date': ('日期_MCC', 80),
                    'msg_aircraft': ('飞机号_MSG', 80),
                    'msg_date': ('日期_MSG', 80),
                    'bak_aircraft': ('飞机号_BAK', 80),
                    'bak_date': ('日期_BAK', 80),
                    'result': ('结果', 80)
                }
            },
            4: {  # 第四步：FIM文件复制
                'title': '第四步：FIM文件复制',
                'columns': ('seq', 'data_name', 'mcc_aircraft', 'mcc_date', 'msg_aircraft', 'msg_date', 'bak_aircraft', 'bak_date', 'result'),
                'column_config': {
                    'seq': ('序号', 60),
                    'data_name': ('PC卡数据', 120),
                    'mcc_aircraft': ('飞机号_MCC', 80),
                    'mcc_date': ('日期_MCC', 80),
                    'msg_aircraft': ('飞机号_MSG', 80),
                    'msg_date': ('日期_MSG', 80),
                    'bak_aircraft': ('飞机号_BAK', 80),
                    'bak_date': ('日期_BAK', 80),
                    'result': ('结果', 80)
                }
            },
            5: {  # 第五步：PC卡数据备份
                'title': '第五步：PC卡数据备份',
                'columns': ('seq', 'data_name', 'mcc_aircraft', 'mcc_date', 'msg_aircraft', 'msg_date', 'bak_aircraft', 'bak_date', 'result'),
                'column_config': {
                    'seq': ('序号', 60),
                    'data_name': ('PC卡数据', 120),
                    'mcc_aircraft': ('飞机号_MCC', 80),
                    'mcc_date': ('日期_MCC', 80),
                    'msg_aircraft': ('飞机号_MSG', 80),
                    'msg_date': ('日期_MSG', 80),
                    'bak_aircraft': ('飞机号_BAK', 80),
                    'bak_date': ('日期_BAK', 80),
                    'result': ('结果', 80)
                }
            }
        }

        # 创建步骤标题
        self.step_title_label = tk.Label(details_frame, text=self.step_configs[1]['title'],
                                        font=('微软雅黑', 12, 'bold'), fg='#2c3e50')
        self.step_title_label.grid(row=0, column=0, columnspan=2, pady=(0, 10), sticky='w')

        # 创建Treeview表格
        current_config = self.step_configs[self.current_step]
        columns = current_config['columns']
        column_config = current_config['column_config']

        self.results_tree = ttk.Treeview(details_frame, columns=columns, show='headings', height=25)

        for col, (heading, width) in column_config.items():
            self.results_tree.heading(col, text=heading)
            if col == 'data_name':
                self.results_tree.column(col, width=width, minwidth=50, anchor='w')
            else:
                self.results_tree.column(col, width=width, minwidth=50, anchor='center')

        # 添加滚动条
        scrollbar_v = ttk.Scrollbar(details_frame, orient=tk.VERTICAL, command=self.results_tree.yview)
        scrollbar_h = ttk.Scrollbar(details_frame, orient=tk.HORIZONTAL, command=self.results_tree.xview)
        self.results_tree.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)

        # 布局（全部使用grid）
        self.results_tree.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar_v.grid(row=1, column=1, sticky=(tk.N, tk.S))
        scrollbar_h.grid(row=2, column=0, sticky=(tk.W, tk.E))

        # 配置行列权重
        details_frame.rowconfigure(1, weight=1)
        details_frame.columnconfigure(0, weight=1)

        # 配置样式
        self.results_tree.tag_configure('processing', background='#e3f2fd', foreground='#1565c0')
        self.results_tree.tag_configure('success', background='#e8f5e8', foreground='#2e7d32')
        self.results_tree.tag_configure('error', background='#ffebee', foreground='#c62828')
        self.results_tree.tag_configure('completed', background='#f3e5f5', foreground='#7b1fa2')

        # 初始化处理结果存储
        self.processing_results = {}
        self.completed_items = {}  # 存储完成的项目及其完成时间

        # 存储各步骤的数据
        self.step_data = {1: [], 2: [], 3: [], 4: [], 5: []}
        self.step_sequence = {1: 0, 2: 0, 3: 0, 4: 0, 5: 0}  # 各步骤的序号计数器

        # 启动清理定时器
        self.start_cleanup_timer()

    def switch_to_step(self, step_num):
        """切换到指定步骤"""
        if step_num not in self.step_configs:
            return

        self.current_step = step_num
        config = self.step_configs[step_num]

        # 更新标题
        self.step_title_label.config(text=config['title'])

        # 清空当前表格
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)

        # 重新配置列
        self.results_tree.config(columns=config['columns'])

        # 重新设置列标题和宽度
        for col, (title, width) in config['column_config'].items():
            self.results_tree.heading(col, text=title)
            if col in ['path', 'name', 'data_name']:  # 左对齐的列
                self.results_tree.column(col, width=width, minwidth=50, anchor='w')
            else:  # 其他列居中对齐（包括序号列）
                self.results_tree.column(col, width=width, minwidth=50, anchor='center')

        # 恢复该步骤的数据
        for data in self.step_data[step_num]:
            self.results_tree.insert('', 'end', values=data['values'], tags=data.get('tags', ()))

    def add_step_result(self, step_num, **kwargs):
        """添加步骤结果"""
        if step_num not in self.step_configs:
            return

        config = self.step_configs[step_num]

        if step_num in [1, 2]:  # 第一步和第二步
            self.step_sequence[step_num] += 1
            values = (
                self.step_sequence[step_num],
                kwargs.get('path', ''),
                kwargs.get('name', ''),
                kwargs.get('type', ''),
                kwargs.get('result', '')
            )
        else:  # 第三、四、五步（也有序号）
            self.step_sequence[step_num] += 1
            values = (
                self.step_sequence[step_num],
                kwargs.get('data_name', ''),
                kwargs.get('mcc_aircraft', ''),
                kwargs.get('mcc_date', ''),
                kwargs.get('msg_aircraft', ''),
                kwargs.get('msg_date', ''),
                kwargs.get('bak_aircraft', ''),
                kwargs.get('bak_date', ''),
                kwargs.get('result', '')
            )

        # 确定标签
        result = kwargs.get('result', '')
        if result == '成功':
            tags = ('success',)
        elif '失败' in result or '错误' in result:
            tags = ('error',)
        else:
            tags = ('processing',)

        # 存储数据
        data_entry = {'values': values, 'tags': tags}
        self.step_data[step_num].append(data_entry)

        # 如果当前显示的是这个步骤，则添加到表格
        if self.current_step == step_num:
            item_id = self.results_tree.insert('', 'end', values=values, tags=tags)
            # 滚动到最新项目
            self.results_tree.see(item_id)

    def add_processing_result(self, data_name, mcc_aircraft='', mcc_date='', msg_aircraft='', msg_date='', bak_aircraft='', bak_date='', result=''):
        """添加处理结果到表格（完全参考PC_BAK.py格式）"""
        try:
            values = (data_name, mcc_aircraft, mcc_date, msg_aircraft, msg_date, bak_aircraft, bak_date, result)
            item_id = self.results_tree.insert('', 'end', values=values)

            # 存储项目信息
            self.processing_results[data_name] = {
                'item_id': item_id,
                'mcc_aircraft': mcc_aircraft,
                'mcc_date': mcc_date,
                'msg_aircraft': msg_aircraft,
                'msg_date': msg_date,
                'bak_aircraft': bak_aircraft,
                'bak_date': bak_date,
                'result': result,
                'start_time': datetime.now()
            }

            # 设置样式（参考PC_BAK.py）
            if result == '成功':
                self.results_tree.item(item_id, tags=('success',))
                self.completed_items[data_name] = datetime.now()
            elif '失败' in result or '错误' in result:
                self.results_tree.item(item_id, tags=('error',))
            else:
                self.results_tree.item(item_id, tags=('processing',))

            # 滚动到最新项目
            self.results_tree.see(item_id)

        except Exception as e:
            logger.error(f"添加处理结果失败: {str(e)}")

    def update_processing_result(self, data_name, **kwargs):
        """更新处理结果"""
        try:
            if data_name in self.processing_results:
                item_info = self.processing_results[data_name]
                item_id = item_info['item_id']

                # 更新数据
                for key, value in kwargs.items():
                    if key in item_info:
                        item_info[key] = value

                # 更新表格显示
                values = (
                    data_name,
                    item_info['mcc_aircraft'],
                    item_info['mcc_date'],
                    item_info['msg_aircraft'],
                    item_info['msg_date'],
                    item_info['bak_aircraft'],
                    item_info['bak_date'],
                    item_info['result']
                )
                self.results_tree.item(item_id, values=values)

                # 更新样式（参考PC_BAK.py）
                result_value = item_info['result']
                if result_value == '成功':
                    self.results_tree.item(item_id, tags=('success',))
                    self.completed_items[data_name] = datetime.now()
                elif '失败' in result_value or '错误' in result_value:
                    self.results_tree.item(item_id, tags=('error',))
                else:
                    self.results_tree.item(item_id, tags=('processing',))

        except Exception as e:
            logger.error(f"更新处理结果失败: {str(e)}")

    def start_cleanup_timer(self):
        """启动清理定时器"""
        self.cleanup_completed_items()
        # 每10分钟检查一次
        self.root.after(600000, self.start_cleanup_timer)

    def cleanup_completed_items(self):
        """清理完成超过1小时的项目"""
        try:
            current_time = datetime.now()
            items_to_remove = []

            for data_name, completion_time in self.completed_items.items():
                # 检查是否超过1小时
                if (current_time - completion_time).total_seconds() > 3600:  # 3600秒 = 1小时
                    items_to_remove.append(data_name)

            # 删除过期项目
            for data_name in items_to_remove:
                if data_name in self.processing_results:
                    item_id = self.processing_results[data_name]['item_id']
                    self.results_tree.delete(item_id)
                    del self.processing_results[data_name]
                    del self.completed_items[data_name]
                    logger.info(f"清理完成项目: {data_name}")

        except Exception as e:
            logger.error(f"清理完成项目失败: {str(e)}")

    def add_log_message(self, message):
        """添加日志消息（兼容性方法）"""
        # 将日志消息转换为状态更新
        if "扫描" in message:
            self.monitoring_status.set("扫描中")
        elif "暂停" in message:
            self.monitoring_status.set("已暂停")
        elif "启动" in message:
            self.monitoring_status.set("扫描中")

        # 记录到系统日志
        logger.info(message)

    def update_gui_callback(self, task, pc_cards, errors):
        """GUI更新回调函数"""
        try:
            self.root.after(0, self._update_gui_safe, task, pc_cards, errors)
        except Exception as e:
            logger.error(f"GUI回调更新失败: {str(e)}")

    def _update_gui_safe(self, task, pc_cards, errors):
        """安全的GUI更新方法"""
        try:
            self.processed_pc_cards.set(pc_cards)
            self.error_count.set(errors)
            self.stats_label.configure(text=f"PC卡: {pc_cards}")
            self.current_task.set(task)

            # 添加日志消息
            self.add_log_message(f"{task} - PC卡:{pc_cards}")

        except Exception as e:
            logger.error(f"GUI安全更新失败: {str(e)}")

    def update_global_paths(self, *args):
        """更新全局路径变量"""
        global PC_MCC_PATH, TEMP_PATH, DATA_BAK_PATH, QAR_PC_PATH
        PC_MCC_PATH = self.pc_mcc_path_var.get()
        TEMP_PATH = self.temp_path_var.get()
        DATA_BAK_PATH = self.data_bak_path_var.get()
        QAR_PC_PATH = self.qar_pc_path_var.get()
        logger.info(f"路径已更新: 监控={PC_MCC_PATH}, 临时={TEMP_PATH}, DATA_BAK={DATA_BAK_PATH}, QAR_PC={QAR_PC_PATH}")
        self.update_actual_paths_display()

    def update_actual_paths_display(self):
        """更新实际使用路径的显示（已禁用）"""
        pass

    def start_monitoring_manual(self):
        """手动启动定时扫描（不使用文件系统监控）"""
        try:
            self.add_log_message("正在启动定时扫描...")

            # 更新全局路径并初始化
            self.update_global_paths()
            initialize_paths()

            # 检查监控路径
            if not os.path.exists(ACTUAL_PC_MCC_PATH):
                self.add_log_message(f"错误：监控路径不存在 - {ACTUAL_PC_MCC_PATH}")
                self.monitoring_status.set("路径不存在")
                return

            self.monitoring_active = True
            self.is_paused = False
            self.add_log_message("定时扫描已启动")
            self.monitoring_status.set("扫描中")

            # 更新按钮状态
            self.start_btn.configure(state='disabled')
            self.pause_btn.configure(state='normal')

            # 更新状态指示器
            self.status_indicator.configure(foreground='#27ae60')

            # 立即执行一次扫描
            self.add_log_message("立即执行首次扫描...")
            threading.Thread(target=self.processor.process_all, daemon=True).start()

            # 启动定时扫描（每30分钟）
            self.start_scheduled_scan()

            logger.info("PC_AUTO_BAK 定时扫描启动完成")

        except Exception as e:
            self.add_log_message(f"启动扫描失败: {str(e)}")
            self.monitoring_status.set("启动失败")
            logger.error(f"启动扫描失败: {str(e)}")

    def pause_monitoring(self):
        """暂停定时扫描"""
        try:
            # 停止定时扫描
            self.stop_scheduled_scan()

            self.monitoring_active = False
            self.is_paused = True
            self.add_log_message("定时扫描已暂停")
            self.monitoring_status.set("已暂停")

            # 更新按钮状态
            self.start_btn.configure(state='normal')
            self.pause_btn.configure(state='disabled')

            # 更新状态指示器
            self.status_indicator.configure(foreground='#e74c3c')

            logger.info("PC_AUTO_BAK 定时扫描已暂停")

        except Exception as e:
            self.add_log_message(f"暂停扫描失败: {str(e)}")
            logger.error(f"暂停扫描失败: {str(e)}")

    def start_scheduled_scan(self):
        """启动定时扫描"""
        self.schedule_next_scan()

    def schedule_next_scan(self):
        """安排下次扫描 - 每天7:00和19:00各触发一次"""
        if not self.is_paused and self.monitoring_active:
            # 计算到下一个7:00或19:00的时间
            now = datetime.now()
            target_hours = [7, 19]  # 早上7点和晚上7点
            
            # 找到今天或明天的下一个目标时间
            next_run = None
            for hour in target_hours:
                target = now.replace(hour=hour, minute=0, second=0, microsecond=0)
                if target > now:
                    next_run = target
                    break
            
            # 如果今天的两个时间都错过了，则设置为明天的7:00
            if next_run is None:
                next_run = now.replace(hour=7, minute=0, second=0, microsecond=0) + timedelta(days=1)
            
            # 计算时间差（秒）
            delay = (next_run - now).total_seconds()
            
            self.scan_timer = threading.Timer(delay, self.scheduled_scan)
            self.scan_timer.daemon = True
            self.scan_timer.start()
            logger.info(f"下次扫描时间: {next_run.strftime('%Y-%m-%d %H:%M:%S')}")

    def scheduled_scan(self):
        """定时扫描执行"""
        if not self.is_paused and self.monitoring_active:
            self.add_log_message("执行定时扫描（每1小时）...")
            threading.Thread(target=self.processor.process_all, daemon=True).start()

            # 安排下次扫描
            self.schedule_next_scan()

    def stop_scheduled_scan(self):
        """停止定时扫描"""
        if self.scan_timer:
            self.scan_timer.cancel()
            self.scan_timer = None

    def set_scan_days(self):
        """设置扫描天数的回调"""
        days = self.scan_days_var.get()
        if self.date_manager.set_scan_days(days):
            # 更新显示
            start_date, end_date = self.date_manager.get_date_range()
            date_range_str = f"{start_date} 到 {end_date}"
            self.date_range_text.set(date_range_str)
            messagebox.showinfo("提示", f"扫描天数已设置为: {days}天")
        else:
            messagebox.showerror("错误", f"无效的扫描天数: {days}\n请输入大于0的整数")

    def update_loop(self):
        """状态更新循环"""
        while True:
            try:
                # 更新运行时间
                uptime_delta = datetime.now() - self.start_time
                uptime_str = str(uptime_delta).split('.')[0]
                self.root.after(0, lambda: self.uptime.set(uptime_str))

                # 更新日期范围
                start_date, end_date = self.date_manager.get_date_range()
                date_range_str = f"{start_date} 到 {end_date}"
                self.root.after(0, lambda: self.date_range_text.set(date_range_str))

                # 更新扫描天数输入框（如果有变化）
                if hasattr(self, 'scan_days_var') and self.scan_days_var.get() != str(self.date_manager.scan_days):
                    self.root.after(0, lambda: self.scan_days_var.set(str(self.date_manager.scan_days)))

                time.sleep(5)  # 每5秒更新一次

            except Exception as e:
                logger.error(f"更新循环错误: {str(e)}")
                time.sleep(10)

    def manual_process(self):
        """手动处理"""
        self.add_log_message("开始手动处理...")
        self.add_log_message(f"当前监控路径: {ACTUAL_PC_MCC_PATH}")
        self.add_log_message(f"当前临时路径: {ACTUAL_TEMP_PATH}")

        # 先检查路径是否存在
        if not os.path.exists(ACTUAL_PC_MCC_PATH):
            self.add_log_message(f"错误: 监控路径不存在 - {ACTUAL_PC_MCC_PATH}")
            return

        # 检查具体的日期目录
        test_date_path = os.path.join(ACTUAL_PC_MCC_PATH, "2025-07", "20250723")
        if os.path.exists(test_date_path):
            file_count = len(os.listdir(test_date_path))
            self.add_log_message(f"找到测试目录: {test_date_path}")
            self.add_log_message(f"目录中文件数量: {file_count}")
        else:
            self.add_log_message(f"测试目录不存在: {test_date_path}")

        threading.Thread(target=self.processor.process_all, daemon=True).start()

    def view_logs(self):
        """查看日志"""
        try:
            if os.path.exists(QAR_PC_LOG_DIR):
                os.startfile(QAR_PC_LOG_DIR)
            else:
                messagebox.showinfo("提示", "日志目录不存在")
        except Exception as e:
            messagebox.showerror("错误", f"打开日志目录失败: {str(e)}")

    def open_monitor_folder(self):
        """打开监控目录"""
        try:
            if os.path.exists(ACTUAL_PC_MCC_PATH):
                os.startfile(ACTUAL_PC_MCC_PATH)
            else:
                messagebox.showwarning("警告", f"监控目录不存在: {ACTUAL_PC_MCC_PATH}")
        except Exception as e:
            messagebox.showerror("错误", f"打开监控目录失败: {str(e)}")


    def on_closing(self):
        """窗口关闭事件"""
        try:
            self.add_log_message("正在退出程序...")

            # 停止定时扫描
            self.stop_scheduled_scan()

            # 停止更新线程
            if hasattr(self, 'update_thread_running'):
                self.update_thread_running = False

            # 保存PC卡计数
            try:
                print(f"[DEBUG] 开始保存PC卡计数")
                logger.info(f"开始保存PC卡计数")
                if hasattr(self, 'processor'):
                    print(f"[DEBUG] processor对象存在")
                    logger.info(f"processor对象存在")
                    if self.processor is not None:
                        print(f"[DEBUG] processor对象不为None")
                        logger.info(f"processor对象不为None")
                        if hasattr(self.processor, 'file_processor'):
                            print(f"[DEBUG] processor有file_processor属性")
                            logger.info(f"processor有file_processor属性")
                            if self.processor.file_processor is not None:
                                print(f"[DEBUG] file_processor对象不为None")
                                logger.info(f"file_processor对象不为None")
                                print(f"[DEBUG] 尝试保存PC卡计数: {self.processor.file_processor.processed_pc_cards}")
                                logger.info(f"尝试保存PC卡计数: {self.processor.file_processor.processed_pc_cards}")
                                self.processor.file_processor.save_processed_pc_cards()
                                self.add_log_message(f"PC卡计数已保存: {self.processor.file_processor.processed_pc_cards}")
                            else:
                                print(f"[DEBUG] 警告: file_processor对象为None，PC卡计数未保存")
                                logger.warning("file_processor对象为None，PC卡计数未保存")
                                self.add_log_message("警告: file_processor对象为None，PC卡计数未保存")
                        else:
                            print(f"[DEBUG] 警告: processor对象没有file_processor属性，PC卡计数未保存")
                            logger.warning("processor对象没有file_processor属性，PC卡计数未保存")
                            self.add_log_message("警告: processor对象没有file_processor属性，PC卡计数未保存")
                    else:
                        print(f"[DEBUG] 警告: processor对象为None，PC卡计数未保存")
                        logger.warning("processor对象为None，PC卡计数未保存")
                        self.add_log_message("警告: processor对象为None，PC卡计数未保存")
                else:
                    print(f"[DEBUG] 警告: 没有processor属性，PC卡计数未保存")
                    logger.warning("没有processor属性，PC卡计数未保存")
                    self.add_log_message("警告: 没有processor属性，PC卡计数未保存")
            except Exception as save_error:
                print(f"[DEBUG] 保存PC卡计数时出错: {str(save_error)}")
                logger.error(f"保存PC卡计数时出错: {str(save_error)}")
                self.add_log_message(f"保存PC卡计数时出错: {str(save_error)}")

            logger.info("PC_AUTO_BAK 程序退出")

            # 退出程序
            self.root.quit()
            self.root.destroy()

        except Exception as e:
            logger.error(f"程序退出时出错: {str(e)}")
        finally:
            os._exit(0)

    def run(self):
        """运行GUI"""
        self.root.mainloop()

# 文件系统监控功能已禁用
# class PCFileHandler(FileSystemEventHandler):
#     """PC文件监控处理器"""
# 
#     def __init__(self, processor, gui=None):
#         self.processor = processor
#         self.gui = gui
#         self.last_process_time = 0
#         self.process_delay = 10  # 10秒延迟
# 
#     def should_ignore_path(self, path):
#         """检查是否应该忽略此路径"""
#         # 忽略Data_Monitor目录及其子目录
#         if "Data_Monitor" in path:
#             return True
#         return False
# 
#     def on_created(self, event):
#         """文件创建事件"""
#         if not event.is_directory and not self.is_paused() and not self.should_ignore_path(event.src_path):
#             self.schedule_process()
# 
#     def on_modified(self, event):
#         """文件修改事件"""
#         if not event.is_directory and not self.is_paused() and not self.should_ignore_path(event.src_path):
#             self.schedule_process()
# 
#     def is_paused(self):
#         """检查是否暂停"""
#         if self.gui:
#             return self.gui.is_paused
#         return False
# 
#     def schedule_process(self):
#         """调度处理"""
#         current_time = time.time()
#         if current_time - self.last_process_time > self.process_delay:
#             self.last_process_time = current_time
#             logger.info("检测到文件变化，启动处理...")
#             threading.Thread(target=self.processor.process_all, daemon=True).start()

def main():
    """主程序入口"""
    print("PC_AUTO_BAK - 文件解压处理工具")
    print("="*50)

    # 文件系统监控已禁用，不再需要watchdog依赖
    print("📝 注意: 文件系统监控已禁用，仅使用定时扫描和手动扫描功能")
    print("📅 定时扫描时间: 每天早上7点和晚上7点各一次")



    # 初始化路径
    initialize_paths()

    # 显示配置信息
    print(f"\n📁 监控路径: {ACTUAL_PC_MCC_PATH}")
    print(f"📁 临时路径: {ACTUAL_TEMP_PATH}")
    print(f"📁 DATA_BAK路径: {ACTUAL_DATA_BAK_PATH}")
    print(f"📁 QAR_PC路径: {ACTUAL_QAR_PC_PATH}")

    print("\n🚀 启动GUI界面...")

    try:
        # 启动GUI
        app = PCAutoUnzipGUI()
        app.run()
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序运行异常: {str(e)}")
        logger.error(f"程序运行异常: {str(e)}")
    finally:
        print("👋 程序已退出")

if __name__ == "__main__":
    main()

