import os
import shutil
import sys
import json
import logging
import time
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                             QPushButton, QLabel, QLineEdit, QFileDialog, QWidget,
                             QMessageBox, QGroupBox, QFrame, QDesktopWidget)
from PyQt5.QtCore import QTimer, Qt
from PyQt5.QtGui import QFont, QPalette, QFontDatabase

# 配置日志记录
log_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'qar_copy.log')
# 确保日志文件是新的，避免乱码问题
if os.path.exists(log_file):
    os.remove(log_file)
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)
logger.info(f"日志文件路径: {log_file}")

# 在程序启动时添加日志
logger.info("程序已启动")
logger.info(f"源路径: {os.path.join(os.path.dirname(os.path.abspath(__file__)), 'Source')}")
logger.info(f"目标路径: D:\\NON-WGL\\CAST")

# 定义常量
FILE_MONITOR_INTERVAL = 5000  # 文件监控间隔(ms)
FILE_WRITE_CHECK_DELAY = 5000  # 文件写入完成检测延时(ms)
SMALL_FILE_THRESHOLD = 10 * 1024 * 1024  # 小文件阈值(10MB)


class FileCopyApp(QMainWindow):
    def __init__(self):
        super().__init__()
        logger.info("初始化FileCopyApp类")
        self.setWindowTitle("ZIP文件定时复制工具")

        # 初始化路径属性
        self.source_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'Source')
        self.target_path = r'D:\NON-WGL\CAST'
        logger.info(f"源路径已初始化: {self.source_path}")
        logger.info(f"目标路径已初始化: {self.target_path}")

        # 初始化配置文件路径
        self.config_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "Non_WGL.json")
        logger.info(f"配置文件路径已初始化: {self.config_file}")

        # 初始化统计属性
        self.total_files = 0
        self.total_copied_files = 0
        self.total_runtime_seconds = 0
        self.avg_waiting_seconds = 0
        logger.info("统计属性已初始化")

        # 初始化状态属性
        self.is_running = False
        self.is_copying = False
        self.start_time = None
        logger.info(f"状态属性已初始化: 运行={self.is_running}, 复制={self.is_copying}")

        # 获取屏幕信息和DPI缩放
        self.setup_display_settings()
        logger.info("显示设置已配置")
        self.init_ui()
        logger.info("UI已初始化")
        self.load_config()
        logger.info("配置已加载")
        self.scan_source_folder()
        logger.info("已扫描源文件夹")
        self.update_file_info()
        logger.info("文件信息已更新")

        # 设置窗口尺寸（根据DPI自适应，调整为更紧凑的尺寸）
        window_width = int(580 * self.scale_factor)
        window_height = int(420 * self.scale_factor)
        self.setGeometry(100, 100, window_width, window_height)

        # 设置窗口样式（DPI自适应）
        padding_size = max(8, int(10 * self.scale_factor))
        border_radius = max(4, int(6 * self.scale_factor))
        margin_size = max(10, int(12 * self.scale_factor))

        self.setStyleSheet(f"""
            QMainWindow {{
                background-color: #f5f5f5;
            }}
            QGroupBox {{
                font-weight: bold;
                border: {max(1, int(2 * self.scale_factor))}px solid #cccccc;
                border-radius: {border_radius}px;
                margin-top: 1ex;
                padding-top: {margin_size}px;
                background-color: white;
                font-size: {self.base_font_size}px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: {margin_size}px;
                padding: 0 {padding_size}px 0 {padding_size}px;
                color: #2c3e50;
                font-size: {self.large_font_size}px;
                font-weight: bold;
            }}
            QLabel {{
                color: #2c3e50;
                font-size: {self.base_font_size}px;
            }}
            QLineEdit {{
                border: 1px solid #bdc3c7;
                border-radius: {max(3, int(4 * self.scale_factor))}px;
                padding: {padding_size}px;
                font-size: {self.base_font_size}px;
                background-color: white;
                min-height: {max(20, int(25 * self.scale_factor))}px;
            }}
            QLineEdit:focus {{
                border-color: #3498db;
                border-width: {max(1, int(2 * self.scale_factor))}px;
            }}
            QPushButton {{
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: {border_radius}px;
                padding: {padding_size}px {int(padding_size * 1.5)}px;
                font-size: {self.base_font_size}px;
                font-weight: bold;
                min-height: {max(25, int(30 * self.scale_factor))}px;
            }}
            QPushButton:hover {{
                background-color: #2980b9;
            }}
            QPushButton:pressed {{
                background-color: #21618c;
            }}
            QPushButton:disabled {{
                background-color: #bdc3c7;
                color: #7f8c8d;
            }}
            QProgressBar {{
                border: 1px solid #bdc3c7;
                border-radius: {border_radius}px;
                text-align: center;
                font-size: {self.base_font_size}px;
                font-weight: bold;
                background-color: #ecf0f1;
                min-height: {max(20, int(25 * self.scale_factor))}px;
            }}
            QProgressBar::chunk {{
                background-color: #27ae60;
                border-radius: {max(3, int(5 * self.scale_factor))}px;
            }}
        """)

        # 初始化其他变量
        self.copied_files = []
        self.current_file_index = 0
        self.timer = QTimer()
        self.timer.timeout.connect(self.copy_next_file)
        self.countdown_timer = QTimer()
        self.countdown_timer.timeout.connect(self.update_countdown)
        self.file_monitor_timer = QTimer()
        self.file_monitor_timer.timeout.connect(self.monitor_source_folder)
        self.folder_check_timer = QTimer()
        self.folder_check_timer.timeout.connect(self.check_target_folder_empty)
        self.waiting_seconds = 0  # 等待时间计数器
        self.last_run_time = ""  # 最后运行时间
        self.pending_files = []  # 待复制文件队列
        self.processing_files = []  # 正在处理的文件
        self.last_file_sizes = {}  # 记录文件大小用于检测写入完成

        # 注意：不再重复初始化已定义的属性
        # 不再重复调用init_ui(), load_config()和scan_source_folder()
        # 这些已在前面的代码中完成

    def setup_display_settings(self):
        """设置显示相关参数"""
        # 获取屏幕信息
        desktop = QDesktopWidget()
        screen_rect = desktop.screenGeometry()
        self.screen_width = screen_rect.width()
        self.screen_height = screen_rect.height()

        # 计算DPI缩放因子
        app = QApplication.instance()
        if app:
            # 获取主屏幕的DPI
            screen = app.primaryScreen()
            dpi = screen.logicalDotsPerInch()
            # 标准DPI是96，计算缩放因子
            self.scale_factor = max(1.0, dpi / 96.0)

            # 对于4K显示器，进一步调整
            if self.screen_width >= 3840:  # 4K或更高分辨率
                self.scale_factor = max(self.scale_factor, 1.5)
            elif self.screen_width >= 2560:  # 2K分辨率
                self.scale_factor = max(self.scale_factor, 1.25)
        else:
            self.scale_factor = 1.0

        # 计算基础字体大小（字体大一号）
        self.base_font_size = max(10, int(11 * self.scale_factor))
        self.large_font_size = max(11, int(13 * self.scale_factor))
        self.huge_font_size = max(36, int(44 * self.scale_factor))

        print(f"屏幕分辨率: {self.screen_width}x{self.screen_height}")
        print(f"DPI缩放因子: {self.scale_factor:.2f}")
        print(f"字体大小: 基础={self.base_font_size}, 大={self.large_font_size}, 超大={self.huge_font_size}")

    def init_ui(self):
        # 主窗口布局
        main_widget = QWidget()
        main_layout = QVBoxLayout()

        # DPI自适应的间距和边距（调整为更紧凑）
        spacing = max(8, int(10 * self.scale_factor))
        margin = max(12, int(15 * self.scale_factor))

        main_layout.setSpacing(spacing)
        main_layout.setContentsMargins(margin, margin, margin, margin)

        # 路径设置分组
        path_group = QGroupBox("路径设置")
        path_layout = QVBoxLayout()
        path_layout.setSpacing(max(8, int(10 * self.scale_factor)))

        # DPI自适应的控件尺寸（调整为更紧凑）
        label_width = max(60, int(70 * self.scale_factor))
        button_width = max(60, int(70 * self.scale_factor))
        input_height = max(25, int(30 * self.scale_factor))
        button_height = max(25, int(30 * self.scale_factor))

        # 源路径设置
        source_layout = QHBoxLayout()
        source_label = QLabel("源文件夹:")
        source_label.setMinimumWidth(label_width)
        source_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.source_edit = QLineEdit(self.source_path)
        self.source_edit.setReadOnly(True)
        self.source_edit.setMinimumHeight(input_height)
        source_button = QPushButton("浏览...")
        source_button.setMinimumWidth(button_width)
        source_button.setMinimumHeight(button_height)
        source_button.clicked.connect(self.select_source_folder)
        source_layout.addWidget(source_label)
        source_layout.addWidget(self.source_edit, 1)
        source_layout.addWidget(source_button)

        # 目标路径设置
        target_layout = QHBoxLayout()
        target_label = QLabel("目标文件夹:")
        target_label.setMinimumWidth(label_width)
        target_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.target_edit = QLineEdit(self.target_path)
        self.target_edit.setReadOnly(True)
        self.target_edit.setMinimumHeight(input_height)
        target_button = QPushButton("浏览...")
        target_button.setMinimumWidth(button_width)
        target_button.setMinimumHeight(button_height)
        target_button.clicked.connect(self.select_target_folder)
        target_layout.addWidget(target_label)
        target_layout.addWidget(self.target_edit, 1)
        target_layout.addWidget(target_button)

        path_layout.addLayout(source_layout)
        path_layout.addLayout(target_layout)
        path_group.setLayout(path_layout)

        # 状态信息分组
        status_group = QGroupBox("复制状态")
        status_layout = QVBoxLayout()
        status_layout.setSpacing(10)

        # 文件信息
        self.file_info_label = QLabel("等待开始...")
        self.file_info_label.setAlignment(Qt.AlignCenter)
        info_padding = max(6, int(8 * self.scale_factor))
        info_radius = max(3, int(4 * self.scale_factor))
        self.file_info_label.setStyleSheet(f"""
            QLabel {{
                font-size: {self.large_font_size}px;
                font-weight: bold;
                color: #34495e;
                padding: {info_padding}px;
                background-color: #ecf0f1;
                border-radius: {info_radius}px;
            }}
        """)

        # 时间信息
        self.time_info_label = QLabel("已复制文件数: 0 | 剩余文件数: 0 | 总运行时间: 00:00:00 | 最后复制时间: --:--:--")
        self.time_info_label.setAlignment(Qt.AlignCenter)
        time_padding = max(4, int(5 * self.scale_factor))
        self.time_info_label.setStyleSheet(f"""
            QLabel {{
                font-size: {self.base_font_size}px;
                color: #7f8c8d;
                padding: {time_padding}px;
            }}
        """)

        # 完成状态信息
        self.completion_label = QLabel("")
        self.completion_label.setAlignment(Qt.AlignCenter)
        self.completion_label.setVisible(False)
        completion_padding = max(8, int(10 * self.scale_factor))
        completion_radius = max(4, int(6 * self.scale_factor))
        self.completion_label.setStyleSheet(f"""
            QLabel {{
                font-size: {self.large_font_size}px;
                font-weight: bold;
                color: #27ae60;
                padding: {completion_padding}px;
                background-color: #d5f4e6;
                border: 2px solid #27ae60;
                border-radius: {completion_radius}px;
                margin: 5px;
            }}
        """)

        status_layout.addWidget(self.file_info_label)
        status_layout.addWidget(self.time_info_label)
        status_layout.addWidget(self.completion_label)
        status_group.setLayout(status_layout)

        # 等待时间显示分组
        countdown_group = QGroupBox("等待时间")
        countdown_layout = QHBoxLayout()
        countdown_layout.setAlignment(Qt.AlignCenter)
        countdown_layout.setSpacing(max(15, int(20 * self.scale_factor)))

        # 当前等待时间
        self.current_wait_label = QLabel("当前等待: 00:00")
        self.current_wait_label.setAlignment(Qt.AlignCenter)
        current_wait_font = QFont()
        current_wait_font.setPointSize(self.large_font_size)
        current_wait_font.setBold(True)
        self.current_wait_label.setFont(current_wait_font)

        # 平均等待时间
        self.avg_wait_label = QLabel("平均等待: 00:00")
        self.avg_wait_label.setAlignment(Qt.AlignCenter)
        avg_wait_font = QFont()
        avg_wait_font.setPointSize(self.large_font_size)
        avg_wait_font.setBold(True)
        self.avg_wait_label.setFont(avg_wait_font)

        countdown_padding = max(10, int(12 * self.scale_factor))
        countdown_margin = max(5, int(7 * self.scale_factor))
        countdown_radius = max(5, int(7 * self.scale_factor))
        countdown_border = max(1, int(2 * self.scale_factor))

        common_style = f"""QLabel {{
    color: #e74c3c;
    background-color: #fdf2f2;
    border: {countdown_border}px solid #fadbd8;
    border-radius: {countdown_radius}px;
    padding: {countdown_padding}px;
    margin: {countdown_margin}px;
    font-size: {self.large_font_size}px;
    font-weight: bold;
}}"""

        self.current_wait_label.setStyleSheet(common_style)
        self.avg_wait_label.setStyleSheet(common_style)

        countdown_layout.addWidget(self.current_wait_label)
        countdown_layout.addWidget(self.avg_wait_label)
        countdown_group.setLayout(countdown_layout)

        # 控制按钮分组
        control_group = QGroupBox("操作控制")
        control_layout = QHBoxLayout()
        control_layout.setSpacing(max(10, int(12 * self.scale_factor)))

        # DPI自适应的按钮尺寸（字体大一号）
        button_height = max(32, int(38 * self.scale_factor))
        button_width = max(90, int(110 * self.scale_factor))
        button_font_size = max(11, int(13 * self.scale_factor))

        self.start_button = QPushButton("🚀 开始监控")
        self.start_button.setMinimumHeight(button_height)
        self.start_button.setMinimumWidth(button_width)
        self.start_button.setStyleSheet(f"""
            QPushButton {{
                background-color: #27ae60;
                font-size: {button_font_size}px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #229954;
            }}
            QPushButton:pressed {{
                background-color: #1e8449;
            }}
        """)
        self.start_button.clicked.connect(self.start_copying)

        self.cancel_button = QPushButton("⏹ 取消操作")
        self.cancel_button.setMinimumHeight(button_height)
        self.cancel_button.setMinimumWidth(button_width)
        self.cancel_button.setStyleSheet(f"""
            QPushButton {{
                background-color: #e74c3c;
                font-size: {button_font_size}px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #c0392b;
            }}
            QPushButton:pressed {{
                background-color: #a93226;
            }}
            QPushButton:disabled {{
                background-color: #bdc3c7;
                color: #7f8c8d;
            }}
        """)
        self.cancel_button.clicked.connect(self.cancel_copying)
        self.cancel_button.setEnabled(False)

        control_layout.addStretch()
        control_layout.addWidget(self.start_button)
        control_layout.addWidget(self.cancel_button)
        control_layout.addStretch()
        control_group.setLayout(control_layout)

        # 添加到主布局
        main_layout.addWidget(path_group)
        main_layout.addWidget(status_group)
        main_layout.addWidget(countdown_group)
        main_layout.addWidget(control_group)
        main_layout.addStretch()

        main_widget.setLayout(main_layout)
        self.setCentralWidget(main_widget)

    def select_source_folder(self):
        folder = QFileDialog.getExistingDirectory(self, "选择源文件夹", self.source_path)
        if folder:
            self.source_path = folder
            self.source_edit.setText(folder)
            self.scan_source_folder()

    def select_target_folder(self):
        folder = QFileDialog.getExistingDirectory(self, "选择目标文件夹", self.target_path)
        if folder:
            self.target_path = folder
            self.target_edit.setText(folder)

    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.total_copied_files = config.get('total_copied_files', 0)
                    self.total_runtime_seconds = config.get('total_runtime_seconds', 0)
                    self.last_run_time = config.get('last_run_time', '')
                    # 计算平均等待时间
                    if self.total_copied_files > 0:
                        self.avg_waiting_seconds = self.total_runtime_seconds / self.total_copied_files
                    else:
                        self.avg_waiting_seconds = 0
                    # 更新UI显示
                    self.update_avg_wait_label()
        except Exception as e:
            print(f"加载配置文件出错: {str(e)}")

    def save_config(self):
        """保存配置文件"""
        try:
            config = {
                'total_copied_files': self.total_copied_files,
                'total_runtime_seconds': self.total_runtime_seconds,
                'last_run_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
        except Exception as e:
            print(f"保存配置文件出错: {str(e)}")

    def monitor_source_folder(self):
        """监控源文件夹，检测新文件"""
        if not self.is_running:
            return

        try:
            # 获取所有ZIP文件
            current_files = [f for f in os.listdir(self.source_path)
                            if f.lower().endswith('.zip') and not f.startswith('~$')]

            # 检查新文件
            for file in current_files:
                if file not in self.processing_files and file not in self.pending_files:
                    # 添加到处理队列
                    self.processing_files.append(file)
                    # 检查文件是否写入完成
                    self.check_file_write_complete(file)

            # 如果有待复制文件且不在复制过程中，则开始复制
            if hasattr(self, 'pending_files') and hasattr(self, 'is_copying'):
                if self.pending_files and not self.is_copying and self.is_running:
                    self.is_copying = True
                    self.copy_files_in_batches()
        except Exception as e:
            print(f"监控源文件夹出错: {str(e)}")

    def check_file_write_complete(self, file):
        """检查文件是否写入完成"""
        if not self.is_running:
            return

        file_path = os.path.join(self.source_path, file)
        try:
            if not os.path.exists(file_path):
                # 文件不存在，可能已被其他进程处理
                if file in self.processing_files:
                    self.processing_files.remove(file)
                if file in self.last_file_sizes:
                    del self.last_file_sizes[file]
                return

            # 获取当前文件大小
            current_size = os.path.getsize(file_path)

            if file in self.last_file_sizes:
                # 如果文件大小没有变化，认为写入完成
                if current_size == self.last_file_sizes[file]:
                    # 从处理队列移除，添加到待复制队列
                    if file in self.processing_files:
                        self.processing_files.remove(file)
                    if file not in self.pending_files:
                        self.pending_files.append(file)
                        self.update_file_info()

                        # 如果不在复制过程中，则开始复制
                        if not self.is_copying and self.is_running:
                            self.is_copying = True
                            self.copy_files_in_batches()
                    return
                else:
                    # 更新记录的文件大小
                    self.last_file_sizes[file] = current_size
            else:
                # 首次检查，记录文件大小
                self.last_file_sizes[file] = current_size

            # 5秒后再次检查
            QTimer.singleShot(FILE_WRITE_CHECK_DELAY, lambda: self.check_file_write_complete(file))
        except Exception as e:
            print(f"检查文件写入完成出错: {str(e)}")
            # 从处理队列移除
            if file in self.processing_files:
                self.processing_files.remove(file)

    def scan_source_folder(self):
        # 确保Copied目录存在
        copied_dir = os.path.join(self.source_path, "Copied")
        if not os.path.exists(copied_dir):
            os.makedirs(copied_dir)

        # 获取所有ZIP文件
        self.zip_files = [f for f in os.listdir(self.source_path)
                          if f.lower().endswith('.zip') and not f.startswith('~$')]
        self.total_files = len(self.zip_files)
        self.current_file_index = 0

        # 初始化待复制文件列表
        self.pending_files = []
        
        # 只添加第一个文件到待复制队列
        if self.zip_files:
            self.pending_files.append(self.zip_files[0])
            self.current_file_index = 1

        # 更新UI
        self.update_file_info()

    def update_file_info(self):
        remaining = len(self.pending_files)
        current_time = datetime.now().strftime('%H:%M:%S')
        total_runtime = time.strftime('%H:%M:%S', time.gmtime(self.total_runtime_seconds))

        self.file_info_label.setText(
            f"待复制文件: {remaining} | 监控中..."
        )

        if self.is_running and self.start_time:
            elapsed = datetime.now() - self.start_time
            self.time_info_label.setText(
                f"已复制文件数: {self.total_copied_files} | 剩余文件数: {remaining} | "
                f"总运行时间: {total_runtime} | 最后复制时间: {current_time}"
            )

    def update_countdown(self):
        """更新等待时间显示"""
        self.waiting_seconds += 1
        # 格式化为 MM:SS
        minutes = self.waiting_seconds // 60
        seconds = self.waiting_seconds % 60
        self.current_wait_label.setText(f"当前等待: {minutes:02d}:{seconds:02d}")

    def update_avg_wait_label(self):
        """更新平均等待时间显示"""
        avg_minutes = int(self.avg_waiting_seconds // 60)
        avg_seconds = int(self.avg_waiting_seconds % 60)
        self.avg_wait_label.setText(f"平均等待: {avg_minutes:02d}:{avg_seconds:02d}")

    def check_target_folder_empty(self):
        """检查目标文件夹是否为空，并在空时添加下一个文件到队列"""
        logger.info(f"进入check_target_folder_empty方法，运行状态: {self.is_running}, 复制状态: {self.is_copying}")

        if not self.is_running or self.is_copying:
            # 如果没有运行或正在复制，则暂停检查
            logger.info("程序未运行或正在复制，暂停检查目标文件夹")
            return

        try:
            # 检查目标文件夹是否为空（包括所有文件类型）
            target_files = [f for f in os.listdir(self.target_path)
                            if not f.startswith('.')]  # 排除隐藏文件

            is_empty = len(target_files) == 0
            logger.info(f"目标文件夹状态: {'空' if is_empty else '非空'}，文件数: {len(target_files)}")
            logger.info(f"目标文件夹文件列表: {target_files}")

            if is_empty:  # 如果目标文件夹为空
                logger.info("目标文件夹为空，准备添加下一个文件")
                # 停止检查定时器
                self.folder_check_timer.stop()
                logger.info("已停止目标文件夹检查定时器")

                # 如果待复制队列为空，且有更多源文件，添加下一个文件
                logger.info(f"待复制文件数: {len(self.pending_files)}, 当前文件索引: {self.current_file_index}, 总源文件数: {len(self.zip_files)}")
                if not self.pending_files and self.current_file_index < len(self.zip_files):
                    next_file = self.zip_files[self.current_file_index]
                    self.pending_files.append(next_file)
                    self.current_file_index += 1
                    self.update_file_info()
                    logger.info(f"添加文件 {next_file} 到待复制队列，新索引: {self.current_file_index}")

                # 如果有待复制文件，准备复制
                if self.pending_files:
                    # 5秒后执行复制
                    self.is_copying = True
                    logger.info(f"有待复制文件 {len(self.pending_files)} 个，5秒后开始复制")
                    QTimer.singleShot(5000, self.copy_files_in_batches)
                else:
                    logger.info("没有更多待复制文件")
            else:
                logger.info(f"目标文件夹不为空，包含 {len(target_files)} 个文件")
                # 10秒后再次检查
                if not self.folder_check_timer.isActive():
                    self.folder_check_timer.start(10000)
                    logger.info("已启动目标文件夹检查定时器，每10秒检查一次")
        except Exception as e:
            logger.error(f"检查目标文件夹出错: {str(e)}")

    def start_copying(self):
        """开始监控源文件夹"""
        if self.total_files == 0:
            QMessageBox.warning(self, "警告", "源文件夹中没有找到ZIP文件!")
            return

        # 确保目标路径存在
        try:
            if not os.path.exists(self.target_path):
                os.makedirs(self.target_path)
                QMessageBox.information(self, "信息", f"目标路径不存在，已自动创建: {self.target_path}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法创建目标路径: {str(e)}")
            return

        self.is_running = True
        self.start_time = datetime.now()
        self.start_button.setEnabled(False)
        self.cancel_button.setEnabled(True)

        # 隐藏完成信息标签
        self.completion_label.setVisible(False)

        # 启动计时器
        self.waiting_seconds = 0
        self.countdown_timer.start(1000)  # 每秒更新一次
        self.file_monitor_timer.start(FILE_MONITOR_INTERVAL)

        # 初始化并启动目标文件夹检查定时器（每10秒检查一次）
        if not hasattr(self, 'folder_check_timer') or not self.folder_check_timer.isActive():
            self.folder_check_timer = QTimer(self)
            self.folder_check_timer.timeout.connect(self.check_target_folder_empty)
            self.folder_check_timer.start(10000)  # 10秒检查一次

        # 立即进行一次检查
        self.check_target_folder_empty()

        self.file_info_label.setText("🔍 正在监控源文件夹...")
        self.update_file_info()

    def copy_files_in_batches(self):
        """复制文件，一次只复制一个"""
        logger.info(f"进入copy_files_in_batches方法，待复制文件: {len(self.pending_files)}, 运行状态: {self.is_running}")

        if not self.pending_files or not self.is_running:
            if self.is_running:
                self.file_info_label.setText("✅ 所有文件复制完成")
                logger.info("所有文件复制完成")
            self.is_copying = False
            logger.info("退出copy_files_in_batches方法")
            return

        # 一次只复制一个文件
        files_to_copy = [self.pending_files.pop(0)]
        logger.info(f"准备复制文件: {files_to_copy[0]}")

        # 开始复制选中的文件
        self.file_info_label.setText(f"🚚 正在复制: {', '.join(files_to_copy)}")
        self.update_file_info()

        # 记录开始复制时间
        copy_start_time = time.time()

        try:
            copied_dir = os.path.join(self.source_path, "Copied")
            if not os.path.exists(copied_dir):
                os.makedirs(copied_dir)
                logger.info(f"创建Copied目录: {copied_dir}")

            for file in files_to_copy:
                source_path = os.path.join(self.source_path, file)
                target_path = os.path.join(self.target_path, file)
                logger.info(f"复制文件: {source_path} -> {target_path}")

                # 检查目标文件是否已存在
                if os.path.exists(target_path):
                    self.file_info_label.setText(f"⚠️ 文件 {file} 已存在，跳过")
                    logger.info(f"文件 {file} 已存在，跳过")
                    continue

                shutil.copy2(source_path, target_path)
                logger.info(f"文件 {file} 复制完成")
                # 移动源文件到Copied目录
                shutil.move(source_path, os.path.join(copied_dir, file))
                logger.info(f"文件 {file} 移动到Copied目录")
                self.total_copied_files += 1
                self.file_info_label.setText(f"✅ 复制完成: {file}")

            # 计算复制时间并更新总运行时间
            copy_duration = time.time() - copy_start_time
            self.total_runtime_seconds += copy_duration
            logger.info(f"复制耗时: {copy_duration:.2f}秒")

            # 更新平均等待时间
            if self.total_copied_files > 0:
                self.avg_waiting_seconds = self.total_runtime_seconds / self.total_copied_files
                self.update_avg_wait_label()
                logger.info(f"平均等待时间: {self.avg_waiting_seconds:.2f}秒")

            # 保存配置
            self.save_config()
            logger.info("配置已保存")

            # 复制完成后，重置复制状态
            self.is_copying = False
            logger.info("复制状态已重置为False")

            # 更新文件信息
            self.update_file_info()

            # 不立即添加下一个文件，而是让check_target_folder_empty决定何时添加
            # 启动目标文件夹检查定时器，如果尚未启动
            if not self.folder_check_timer.isActive() and self.is_running:
                self.folder_check_timer.start(10000)  # 10秒检查一次
                logger.info("启动目标文件夹检查定时器，每10秒检查一次")
            
            # 立即检查目标文件夹是否为空
            logger.info("立即检查目标文件夹是否为空")
            self.check_target_folder_empty()

        except Exception as e:
            self.file_info_label.setText(f"❌ 复制失败: {str(e)}")
            logger.error(f"复制失败: {str(e)}")
            # 失败后重置复制状态
            self.is_copying = False
            logger.info("复制状态已重置为False")
            # 启动目标文件夹检查定时器
            if not self.folder_check_timer.isActive() and self.is_running:
                self.folder_check_timer.start(10000)
                logger.info("启动目标文件夹检查定时器，每10秒检查一次")
                self.folder_check_timer.start(10000)

    def copy_next_file(self):
        # 兼容旧调用，重定向到批量复制方法
        self.copy_files_in_batches()

    def cancel_copying(self):
        self.is_running = False
        self.timer.stop()
        self.countdown_timer.stop()
        self.folder_check_timer.stop()
        self.cancel_button.setEnabled(False)
        self.start_button.setEnabled(True)

        # 在原窗体上显示取消信息
        self.completion_label.setText("⚠️ 复制操作已取消")
        self.completion_label.setStyleSheet(f"""
            QLabel {{
                font-size: {self.large_font_size}px;
                font-weight: bold;
                color: #e74c3c;
                padding: {max(8, int(10 * self.scale_factor))}px;
                background-color: #fdf2f2;
                border: 2px solid #e74c3c;
                border-radius: {max(4, int(6 * self.scale_factor))}px;
                margin: 5px;
            }}
        """)
        self.completion_label.setVisible(True)

    def finish_copying(self):
        """完成复制或取消操作"""
        self.is_running = False
        self.is_copying = False
        self.timer.stop()
        self.countdown_timer.stop()
        self.folder_check_timer.stop()
        self.file_monitor_timer.stop()
        self.cancel_button.setEnabled(False)
        self.start_button.setEnabled(True)

        # 保存配置
        self.save_config()

        # 在原窗体上显示完成信息
        if self.pending_files:
            self.completion_label.setText("🛑 已取消操作")
        else:
            self.completion_label.setText("✅ 所有文件已复制完成!")

        self.completion_label.setVisible(True)

    def __del__(self):
        # 确保在对象销毁时停止所有定时器并保存配置
        if hasattr(self, 'timer'):
            self.timer.stop()
        if hasattr(self, 'countdown_timer'):
            self.countdown_timer.stop()
        if hasattr(self, 'folder_check_timer'):
            self.folder_check_timer.stop()
        if hasattr(self, 'file_monitor_timer'):
            self.file_monitor_timer.stop()

        # 保存配置
        if hasattr(self, 'save_config'):
            self.save_config()


def setup_high_dpi_support():
    """设置高DPI支持和字体渲染优化"""
    # 启用高DPI支持
    if hasattr(Qt, 'AA_EnableHighDpiScaling'):
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)

    if hasattr(Qt, 'AA_UseHighDpiPixmaps'):
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

    # 设置DPI缩放策略
    if hasattr(QApplication, 'setHighDpiScaleFactorRoundingPolicy'):
        QApplication.setHighDpiScaleFactorRoundingPolicy(Qt.HighDpiScaleFactorRoundingPolicy.PassThrough)

    # 启用字体抗锯齿和渲染优化
    if hasattr(Qt, 'AA_UseDesktopOpenGL'):
        QApplication.setAttribute(Qt.AA_UseDesktopOpenGL, True)

    # 启用字体子像素渲染
    if hasattr(Qt, 'AA_UseSoftwareOpenGL'):
        QApplication.setAttribute(Qt.AA_UseSoftwareOpenGL, False)


if __name__ == "__main__":
    # 在创建QApplication之前设置高DPI支持
    setup_high_dpi_support()

    app = QApplication(sys.argv)

    # 设置默认字体（确保中文显示正常）
    font_db = QFontDatabase()

    # 尝试设置系统默认字体
    default_font = QFont()
    default_font.setFamily("Microsoft YaHei UI")  # 微软雅黑
    if not font_db.families().__contains__("Microsoft YaHei UI"):
        default_font.setFamily("SimHei")  # 黑体
    if not font_db.families().__contains__("SimHei"):
        default_font.setFamily("Arial Unicode MS")  # 备用字体

    default_font.setHintingPreference(QFont.PreferFullHinting)
    default_font.setStyleStrategy(QFont.PreferAntialias)
    app.setFont(default_font)

    # 创建并显示窗口
    window = FileCopyApp()
    window.show()

    # 居中显示窗口
    desktop = QDesktopWidget()
    screen_rect = desktop.screenGeometry()
    window_rect = window.geometry()
    x = (screen_rect.width() - window_rect.width()) // 2
    y = (screen_rect.height() - window_rect.height()) // 2
    window.move(x, y)

    sys.exit(app.exec_())
