import os
import sys
import json
import shutil
import time
import datetime
import random
from datetime import timedelta
import schedule
import tkinter as tk
from tkinter import ttk, scrolledtext, font
import threading
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import ctypes
import traceback

# 添加日志记录文件路径信息
print(f"QAR_Datamation.py文件位置: {os.path.abspath(__file__)}")
print(f"当前工作目录: {os.getcwd()}")

# 设置程序图标
try:
    ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID('myappid.QAR_Datamation')
except:
    pass

# 确保中文显示正常
# tk.font.families()  # 移到UI创建后调用

class CountManager:
    def __init__(self, file_path=None):
        # 如果未提供文件路径，使用程序/可执行文件所在目录下的count.json
        if file_path is None:
            # 判断是否是exe运行模式
            if hasattr(sys, 'frozen'):
                # 获取exe所在目录
                current_dir = os.path.dirname(sys.executable)
            else:
                # 获取当前文件所在目录
                current_dir = os.path.dirname(os.path.abspath(__file__))
            self.file_path = os.path.join(current_dir, 'count.json')
        else:
            self.file_path = file_path
        # 记录文件路径日志
        print(f"CountManager使用的文件路径: {self.file_path}")
        # 确保文件存在并初始化
        if not os.path.exists(self.file_path):
            self._create_default_file()

    def _create_default_file(self):
        default_data = {
            'pc_count': 0,
            'enc_count': 0
        }
        with open(self.file_path, 'w', encoding='utf-8') as f:
            json.dump(default_data, f, ensure_ascii=False, indent=2)

    def get_pc_count(self):
        try:
            with open(self.file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data.get('pc_count', 0)
        except:
            return 0

    def set_pc_count(self, count):
        try:
            with open(self.file_path, 'r+', encoding='utf-8') as f:
                data = json.load(f)
                data['pc_count'] = count
                f.seek(0)
                json.dump(data, f, ensure_ascii=False, indent=2)
                f.truncate()
        except:
            # 如果读取失败，创建新文件
            self._create_default_file()
            with open(self.file_path, 'r+', encoding='utf-8') as f:
                data = json.load(f)
                data['pc_count'] = count
                f.seek(0)
                json.dump(data, f, ensure_ascii=False, indent=2)
                f.truncate()

    def get_enc_count(self):
        try:
            with open(self.file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data.get('enc_count', 0)
        except:
            return 0

    def set_enc_count(self, count):
        try:
            with open(self.file_path, 'r+', encoding='utf-8') as f:
                data = json.load(f)
                data['enc_count'] = count
                f.seek(0)
                json.dump(data, f, ensure_ascii=False, indent=2)
                f.truncate()
        except:
            # 如果读取失败，创建新文件
            self._create_default_file()
            with open(self.file_path, 'r+', encoding='utf-8') as f:
                data = json.load(f)
                data['enc_count'] = count
                f.seek(0)
                json.dump(data, f, ensure_ascii=False, indent=2)
                f.truncate()

class TimeManager:
    def __init__(self, file_path=None):
        # 如果未提供文件路径，使用程序/可执行文件所在目录下的last_time.json
        if file_path is None:
            # 判断是否是exe运行模式
            if hasattr(sys, 'frozen'):
                # 获取exe所在目录
                current_dir = os.path.dirname(sys.executable)
            else:
                # 获取当前文件所在目录
                current_dir = os.path.dirname(os.path.abspath(__file__))
            self.file_path = os.path.join(current_dir, 'last_time.json')
        else:
            self.file_path = file_path
        # 记录文件路径日志
        print(f"TimeManager使用的文件路径: {self.file_path}")
        # 确保文件存在并初始化
        if not os.path.exists(self.file_path):
            self._create_default_file()

    def _create_default_file(self):
        default_data = {
            'last_run_time': ''
        }
        with open(self.file_path, 'w', encoding='utf-8') as f:
            json.dump(default_data, f, ensure_ascii=False, indent=2)

    def get_last_run_time(self):
        try:
            with open(self.file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data.get('last_run_time', '')
        except:
            return ''

    def set_last_run_time(self, run_time):
        try:
            with open(self.file_path, 'r+', encoding='utf-8') as f:
                data = json.load(f)
                data['last_run_time'] = run_time
                f.seek(0)
                json.dump(data, f, ensure_ascii=False, indent=2)
                f.truncate()
        except:
            # 如果读取失败，创建新文件
            self._create_default_file()
            with open(self.file_path, 'r+', encoding='utf-8') as f:
                data = json.load(f)
                data['last_run_time'] = run_time
                f.seek(0)
                json.dump(data, f, ensure_ascii=False, indent=2)
                f.truncate()

    # 为兼容旧代码保留的方法
    def get_last_timestamp(self):
        last_run_time = self.get_last_run_time()
        if last_run_time:
            try:
                dt = datetime.datetime.strptime(last_run_time, '%Y-%m-%d %H:%M:%S')
                return int(dt.timestamp())
            except:
                pass
        return 0

    def set_last_timestamp(self, timestamp):
        # 转换时间戳为具体时间格式
        run_time = datetime.datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
        self.set_last_run_time(run_time)

class FolderMonitor:
    def __init__(self, path, callback, is_pc=True):
        self.path = path
        self.callback = callback
        self.is_pc = is_pc
        self.observer = Observer()
        self.event_handler = self._create_event_handler()

    def _create_event_handler(self):
        class Handler(FileSystemEventHandler):
            def __init__(self, monitor):
                self.monitor = monitor

            def on_created(self, event):
                if event.is_directory:
                    # 只处理新创建的文件夹
                    self.monitor.callback(event.src_path, self.monitor.is_pc)

        return Handler(self)

    def start(self):
        if os.path.exists(self.path):
            self.observer.schedule(self.event_handler, self.path, recursive=False)
            self.observer.start()
            return True
        return False

    def stop(self):
        if self.observer.is_alive():
            self.observer.stop()
            self.observer.join()

class DataProcessor:
    def __init__(self, app):
        self.app = app
        self.time_manager = TimeManager()
        self.ac_type_map = self._load_ac_type_map()
        self.processed_folders = set()

    def get_last_run_timestamp(self):
        return self.time_manager.get_last_timestamp()

    def _load_ac_type_map(self):
        try:
            with open('Z:\DATA_BAK\config\AC_TYPE.json', 'r', encoding='utf-8-sig') as f:
                return json.load(f)
        except Exception as e:
            self.app.log_message(f'错误 - 无法加载AC_TYPE.json: {str(e)}')
            return {}

    def check_folder_written(self, folder_path):
        """检查文件夹是否写入完毕
        通过多种机制确保文件完全写入：
        1. 连续检测文件夹大小变化
        2. 检测文件是否被锁定
        3. 检测文件修改时间稳定性
        """
        folder_name = os.path.basename(folder_path)
        self.app.log_message(f'开始检测文件夹 [{folder_name}] 写入状态')

        # 初始化变量
        prev_size = -1
        stable_count = 0
        max_stable_count = 5  # 增加连续稳定次数
        check_interval = 1.0  # 增加检测间隔时间
        file_mod_times = {}  # 存储文件修改时间

        while stable_count < max_stable_count:
            # 检测1: 文件夹大小变化
            current_size = self._get_folder_size(folder_path)
            size_stable = current_size == prev_size

            # 检测2: 文件锁定状态
            all_files_unlocked = self._check_files_unlocked(folder_path)

            # 检测3: 文件修改时间稳定性
            mod_times_stable = self._check_mod_times_stable(folder_path, file_mod_times)

            # 只有当所有检测都稳定时，才增加稳定计数
            if size_stable and all_files_unlocked and mod_times_stable:
                stable_count += 1
                self.app.log_message(f'调试 - {folder_name} 写入状态稳定计数: {stable_count}/{max_stable_count}')
            else:
                stable_count = 0
                prev_size = current_size
                file_mod_times.clear()  # 重置修改时间记录
                self.app.log_message(f'调试 - {folder_name} 写入状态变化，重置计数')

            time.sleep(check_interval)

        self.app.log_message(f'信息 - {folder_name} 文件写入完毕')
        return True

    def _check_files_unlocked(self, folder_path):
        """检查文件夹内所有文件是否已解锁"""
        try:
            for dirpath, _, filenames in os.walk(folder_path):
                for f in filenames:
                    file_path = os.path.join(dirpath, f)
                    # 尝试以读写模式打开文件，如果成功则表示文件未被锁定
                    try:
                        with open(file_path, 'r+b') as test_file:
                            pass
                    except (IOError, PermissionError):
                        # 文件被锁定或无法访问
                        return False
            return True
        except Exception as e:
            self.app.log_message(f'错误 - 检查文件锁定状态失败: {str(e)}')
            return False

    def _check_mod_times_stable(self, folder_path, mod_times_dict):
        """检查文件夹内文件修改时间是否稳定"""
        try:
            current_mod_times = {}
            for dirpath, _, filenames in os.walk(folder_path):
                for f in filenames:
                    file_path = os.path.join(dirpath, f)
                    try:
                        mod_time = os.path.getmtime(file_path)
                        current_mod_times[file_path] = mod_time
                    except Exception as e:
                        self.app.log_message(f'警告 - 无法获取文件修改时间 [{file_path}]: {str(e)}')
                        return False

            # 如果是第一次记录，保存当前修改时间
            if not mod_times_dict:
                mod_times_dict.update(current_mod_times)
                return True

            # 检查所有文件修改时间是否与上次记录一致
            for file_path, mod_time in current_mod_times.items():
                if file_path not in mod_times_dict or mod_times_dict[file_path] != mod_time:
                    return False

            return True
        except Exception as e:
            self.app.log_message(f'错误 - 检查文件修改时间失败: {str(e)}')
            return False

    def _get_folder_size(self, folder_path):
        total_size = 0
        for dirpath, _, filenames in os.walk(folder_path):
            for f in filenames:
                fp = os.path.join(dirpath, f)
                if os.path.isfile(fp):
                    total_size += os.path.getsize(fp)
        return total_size

    def process_folder(self, folder_path, is_pc):
        self.is_pc = is_pc
        folder_name = os.path.basename(folder_path)
        self.app.log_message(f'开始处理文件夹 [{folder_name}]')

        # 检查是否已处理过
        if folder_name in self.processed_folders:
            self.app.log_message(f'跳过文件夹 [{folder_name}]，已处理过')
            return

        # 检查文件夹创建时间
        try:
            created_time = os.path.getctime(folder_path)
            last_run_timestamp = self.get_last_run_timestamp()
            if created_time < last_run_timestamp:
                # 忽略上次停止前创建的文件夹
                self.app.log_message(f'跳过文件夹 [{folder_name}]，创建时间早于上次停止时间')
                return
        except Exception as e:
            self.app.log_message(f'错误 - 无法获取文件夹 [{folder_name}] 创建时间: {str(e)}')
            # 即使无法获取创建时间，也继续处理该文件夹

        self.app.update_status(f'发现{"PC" if is_pc else "ENC"}新文件 [{folder_name}]')

        # 识别飞机号
        if len(folder_name) >= 6:
            ac_tail = folder_name[:6]
            if ac_tail.startswith('B-') and len(ac_tail) == 6:
                # 查找机型
                ac_type = self.ac_type_map.get(ac_tail, 'Unknown')
                if ac_type == 'Unknown':
                    self.app.log_message(f'警告 - 无法找到飞机号 {ac_tail} 对应的机型')
                    return

                # 检查文件是否写入完毕
                if not self.check_folder_written(folder_path):
                    return

                # 确定目标路径
                if is_pc:
                    # PC卡路径: D:\AirFASE\FIMRoot\[ac_type]\[ac_tail]\[folder_name]
                    target_path = os.path.join('D:\AirFASE\FIMRoot', ac_type, ac_tail, folder_name)
                    # 添加调试信息
                    self.app.log_message(f'调试 - 处理PC文件，文件夹名: {folder_name}，飞机号: {ac_tail}，机型: {ac_type}，目标路径: {target_path}')
                else:
                    # ENC文件处理
                    new_folder_name = folder_name + '.enc'
                    target_path = os.path.join('D:\AirFASE\FIMRoot', f'WGL_{ac_type}', ac_tail, new_folder_name)

                # 检查目标路径是否存在
                if os.path.exists(target_path):
                    return

                # 创建目标目录
                try:
                    os.makedirs(os.path.dirname(target_path), exist_ok=True)
                except Exception as e:
                    self.app.log_message(f'无法创建目标目录: {str(e)}', level='error')
                    return

                # 复制文件夹
                self.app.update_status(f'正在复制{"PC" if is_pc else "ENC"}文件 [{folder_name}]')
                try:
                    # 复制整个文件夹
                    shutil.copytree(folder_path, target_path)
                    self.app.log_message(f'成功复制文件夹: {folder_name}', level='success')
                    self.processed_folders.add(folder_name)

                    # 更新统计 (以文件夹为单位)
                    if is_pc:
                        current_count = self.app.count_manager.get_pc_count() + 1
                        self.app.count_manager.set_pc_count(current_count)
                        self.app.update_pc_count(current_count)
                    else:
                        current_count = self.app.count_manager.get_enc_count() + 1
                        self.app.count_manager.set_enc_count(current_count)
                        self.app.update_enc_count(current_count)

                    # 更新最后运行时间（文件复制操作时间）
                    current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    self.app.last_run_var.set(f'最后运行: {current_time}')

                    # 复制完成后更新状态为等待文件
                    self.app.update_status('新文件持续监控中')

                except Exception as e:
                    self.app.log_message(f'处理文件夹 [{folder_name}] 时出错: {str(e)}', level='error')
                    self.app.log_message(f'错误详情: {traceback.format_exc()}', level='error')
                return
            else:
                self.app.log_message(f'警告 - 文件夹名 {folder_name} 不符合飞机号格式(B-xxxx)')
        else:
            self.app.log_message(f'警告 - 文件夹名 {folder_name} 长度不足，无法提取飞机号')

    def cleanup_old_data(self):
        # 清理前一天复制的数据
        self.app.update_status('正在执行定期清理任务')
        root_path = 'D:\AirFASE\FIMRoot'

        # 获取当前日期和前一天日期（日期格式：YYYY-MM-DD）
        today = datetime.datetime.now().date()
        yesterday = today - timedelta(days=1)
        yesterday_str = yesterday.strftime('%Y-%m-%d')

        try:
            for ac_type in os.listdir(root_path):
                ac_type_path = os.path.join(root_path, ac_type)
                if os.path.isdir(ac_type_path):
                    for ac_tail in os.listdir(ac_type_path):
                        ac_tail_path = os.path.join(ac_type_path, ac_tail)
                        if os.path.isdir(ac_tail_path):
                            self.app.update_status(f'正在检查 {ac_type} 类型飞机 [{ac_tail}] 下的数据文件夹')
                            
                            # 遍历飞机号文件夹下的所有子文件夹
                            for sub_folder in os.listdir(ac_tail_path):
                                sub_folder_path = os.path.join(ac_tail_path, sub_folder)
                                if os.path.isdir(sub_folder_path):
                                    try:
                                        # 获取文件夹创建日期
                                        created_time = os.path.getctime(sub_folder_path)
                                        created_date = datetime.datetime.fromtimestamp(created_time).date()
                                        created_date_str = created_date.strftime('%Y-%m-%d')

                                        # 检查是否为前一天创建的文件夹
                                        if created_date == yesterday:
                                            self.app.update_status(f'正在删除 {ac_type} 类型飞机 [{ac_tail}] 下的过期数据文件夹 [{sub_folder}]')
                                            shutil.rmtree(sub_folder_path)
                                            self.app.log_message(f'已删除前一天数据文件夹: {sub_folder_path}', level='info')
                                    except Exception as e:
                                        self.app.log_message(f'无法删除文件夹 {sub_folder_path}: {str(e)}', level='error')
        except Exception as e:
            self.app.log_message(f'清理旧数据时发生异常: {str(e)}', level='error')

        self.app.update_status('新文件持续监控中')

class QARApp:
    def __init__(self, root):
        self.root = root
        self.root.title('QAR_Datamation 飞机QAR数据自动抓取工具')
        self.root.geometry('850x650')
        self.root.minsize(700, 500)
        self.root.configure(bg='#f0f2f5')

        # 设置图标
        try:
            self.root.iconbitmap('app.ico')
        except Exception as e:
            print(f'无法设置图标: {e}')

        # 初始化配置管理器
        self.count_manager = CountManager()
        self.time_manager = TimeManager()
        self.pc_count = self.count_manager.get_pc_count()
        self.enc_count = self.count_manager.get_enc_count()

        # UI元素变量初始化
        self.setup_variables()

        # 先创建UI，确保log_text已初始化
        self.setup_styles()
        self._create_ui()

        # 然后记录日志
        last_run_time = self.time_manager.get_last_run_time()
        if last_run_time:
            self.log_message(f'上次运行时间: {last_run_time}', level='info')
        else:
            self.log_message('首次运行程序', level='info')

        # 创建数据处理器
        self.data_processor = DataProcessor(self)

        # 创建监控器
        self.pc_monitor = FolderMonitor('Z:\DATA_BAK\QAR_PC', self.on_new_folder, is_pc=True)
        self.enc_monitor = FolderMonitor('Z:\DATA_BAK\ENC_BAK', self.on_new_folder, is_pc=False)

        # 启动时间计数
        self.start_time = time.time()
        self.update_runtime()

        # 默认状态为暂停
        self.is_running = False
        self.status_var.set('监控已暂停')
        self.pause_button.config(state=tk.DISABLED)
        self.run_button.config(state=tk.NORMAL)
        self.log_message('程序已初始化，监控处于暂停状态', level='info')

        # 设置定时任务
        schedule.every().day.at('06:00').do(self.data_processor.cleanup_old_data)
        self.start_schedule_thread()

        # 记录当前运行时间
        current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.log_message(f'当前运行时间: {current_time}')

        # 启动状态更新线程
        self.update_thread = threading.Thread(target=self.update_loop, daemon=True)
        self.update_thread.start()

    def setup_variables(self):
        """设置变量"""
        self.status_var = tk.StringVar(value='监控已暂停')
        self.runtime_var = tk.StringVar(value='运行时间: 00:00:00')
        self.last_run_var = tk.StringVar()
        self.pc_count_var = tk.StringVar(value=str(self.pc_count))
        self.enc_count_var = tk.StringVar(value=str(self.enc_count))
        self.current_task = tk.StringVar(value="监控中")
        self.error_count = tk.IntVar(value=0)
        self.last_activity = tk.StringVar(value="刚刚")

    def setup_styles(self):
        """设置样式"""
        self.style = ttk.Style()
        self.style.theme_use('clam')

        self.style.configure('Title.TLabel', font=('Microsoft YaHei', 16, 'bold'),
                           background='#f0f2f5', foreground='#2c3e50')
        self.style.configure('Header.TLabel', font=('Microsoft YaHei', 12, 'bold'),
                           background='#f0f2f5', foreground='#34495e')
        self.style.configure('Value.TLabel', font=('Microsoft YaHei', 11, 'bold'),
                           background='#f0f2f5', foreground='#2980b9')
        self.style.configure('Action.TButton', font=('Microsoft YaHei', 10, 'bold'),
                           padding=(20, 10))
        self.style.configure('Success.TButton', foreground='white', background='#4CAF50')
        self.style.configure('Warning.TButton', foreground='white', background='#FF9800')
        self.style.configure('Danger.TButton', foreground='white', background='#F44336')

    def _create_ui(self):
        """创建界面组件"""
        main_frame = ttk.Frame(self.root, padding="15")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题区域
        self.create_header(main_frame)
        # 状态卡片区域
        self.create_status_cards(main_frame)
        # 控制按钮区域
        self.create_control_buttons(main_frame)
        # 详细信息区域
        self.create_details_section(main_frame)

    def create_header(self, parent):
        """创建标题区域"""
        header_frame = ttk.Frame(parent)
        header_frame.pack(fill=tk.X, pady=(0, 15))

        title_label = ttk.Label(header_frame, text="🛡️ QAR DATAMATION",
                               style='Title.TLabel')
        title_label.pack(side=tk.LEFT)

        self.status_indicator = ttk.Label(header_frame, text="●",
                                         font=('Microsoft YaHei', 16),
                                         foreground='#e74c3c')  # 默认红色表示暂停
        self.status_indicator.pack(side=tk.RIGHT, padx=(8, 0))

        self.status_text = ttk.Label(header_frame, text="监控已暂停",
                                    style='Header.TLabel')
        self.status_text.pack(side=tk.RIGHT)

    def create_status_cards(self, parent):
        """创建状态卡片"""
        cards_frame = ttk.Frame(parent)
        cards_frame.pack(fill=tk.X, pady=(0, 15))

        for i in range(3):
            cards_frame.columnconfigure(i, weight=1)

        # 运行时间卡片
        self.create_card(cards_frame, "⏱️ 运行时间", self.runtime_var, 0, 0)

        # 当前任务卡片
        self.create_card(cards_frame, "🔄 当前状态", self.status_var, 0, 1)

        # 上次停止卡片
        last_stop_time = self.time_manager.get_last_run_time()
        self.last_stop_var = tk.StringVar(value=f'本次开始: {last_stop_time if last_stop_time else "首次运行"}')
        self.create_card(cards_frame, "⏱️ 上次停止", self.last_stop_var, 0, 2)

        # PC卡和ENC文件统计卡片（使用两个内容框显示）
        self.stats_card = self.create_card(cards_frame, "📊 数据统计", "", 1, 0, columnspan=2)
        self.create_data_stats_display()

        # 最后运行卡片 (最后进行文件复制操作的时间)
        self.last_run_var = tk.StringVar(value='最后运行: 从未运行')
        self.create_card(cards_frame, "📅 最后运行", self.last_run_var, 1, 2)

    def create_card(self, parent, title, value_var, row, col, columnspan=1):
        """创建单个状态卡片"""
        # 为数据统计使用更紧凑的样式
        if title == "📊 数据统计":
            padding = "5"  # 减少内边距
            height = 60  # 固定高度
        elif title == "📅 最后运行":
            padding = "8"
            height = 50
        else:
            padding = "12"  # 其他卡片适中内边距
            height = 50

        card_frame = ttk.LabelFrame(parent, text=title, padding=padding)
        card_frame.grid(row=row, column=col, columnspan=columnspan,
                       padx=3, pady=3, sticky="ew", ipady=2)

        # 设置最小高度
        card_frame.grid_propagate(False)
        card_frame.configure(height=height)

        if title == "📊 数据统计":
            return card_frame  # 返回框架用于后续添加内容

        if isinstance(value_var, tk.StringVar):
            value_label = ttk.Label(card_frame, textvariable=value_var,
                                   style='Value.TLabel', anchor='center')
        else:
            value_label = ttk.Label(card_frame, textvariable=value_var,
                                   style='Value.TLabel', anchor='center')
        value_label.pack(expand=True)

        return card_frame

    def create_data_stats_display(self):
        """创建数据统计显示区域"""
        # 在stats_card中创建两个内容框
        stats_inner_frame = ttk.Frame(self.stats_card)
        stats_inner_frame.pack(fill=tk.BOTH, expand=True, pady=2)

        # PC卡数据框
        pc_frame = ttk.Frame(stats_inner_frame, relief=tk.RAISED, borderwidth=1)
        pc_frame.pack(side=tk.LEFT, padx=3, expand=True, fill=tk.BOTH)

        ttk.Label(pc_frame, text='PC卡数据', font=('Microsoft YaHei', 9, 'bold')).pack(pady=(3, 1))
        ttk.Label(pc_frame, textvariable=self.pc_count_var, font=('Microsoft YaHei', 20, 'bold'),
                 foreground='#2E7D32').pack(pady=(1, 3))

        # ENC文件数据框
        enc_frame = ttk.Frame(stats_inner_frame, relief=tk.RAISED, borderwidth=1)
        enc_frame.pack(side=tk.LEFT, padx=3, expand=True, fill=tk.BOTH)

        ttk.Label(enc_frame, text='ENC文件数据', font=('Microsoft YaHei', 9, 'bold')).pack(pady=(3, 1))
        ttk.Label(enc_frame, textvariable=self.enc_count_var, font=('Microsoft YaHei', 20, 'bold'),
                 foreground='#1976D2').pack(pady=(1, 3))

    def create_control_buttons(self, parent):
        """创建控制按钮区域"""
        buttons_frame = ttk.Frame(parent)
        buttons_frame.pack(pady=12)

        # 运行按钮
        self.run_button = ttk.Button(buttons_frame, text='🚀 开始监控',
                                    command=self.start_monitoring,
                                    style='Action.TButton',
                                    width=12)
        self.run_button.pack(side=tk.LEFT, padx=8)

        # 暂停按钮
        self.pause_button = ttk.Button(buttons_frame, text='⏸️ 暂停监控',
                                     command=self.pause_monitoring,
                                     style='Action.TButton',
                                     width=12)
        self.pause_button.pack(side=tk.LEFT, padx=8)
        self.pause_button.config(state=tk.DISABLED)  # 初始状态暂停按钮禁用

        # 退出按钮
        self.exit_button = ttk.Button(buttons_frame, text='🚪 退出程序',
                                    command=self.exit_app,
                                    style='Action.TButton',
                                    width=12)
        self.exit_button.pack(side=tk.LEFT, padx=8)

    def create_details_section(self, parent):
        """创建详细信息区域"""
        details_frame = ttk.LabelFrame(parent, text="📋 运行日志", padding="8")
        details_frame.pack(fill=tk.BOTH, expand=True, pady=(5, 0))

        # 创建带滚动条的文本区域
        log_frame = ttk.Frame(details_frame)
        log_frame.pack(fill=tk.BOTH, expand=True)

        # 使用ScrolledText自带的滚动条
        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD,
                                                 font=('Microsoft YaHei', 9),
                                                 bg='#f8f8f8', fg='#333',
                                                 height=12)
        self.log_text.pack(fill=tk.BOTH, expand=True)

        # 初始化日志
        self.log_message('应用程序初始化成功')



    def update_status(self, status):
        self.status_var.set(status)
        self.current_task.set(status)
        self.last_activity.set("刚刚")
        self.log_message(f'信息 - {status}')

        # 更新状态指示器
        if "监控中" in status or "运行" in status:
            self.status_indicator.config(foreground='#27ae60')  # 绿色
            self.status_text.config(text="监控运行中")
        elif "暂停" in status:
            self.status_indicator.config(foreground='#e74c3c')  # 红色
            self.status_text.config(text="监控已暂停")
        else:
            self.status_indicator.config(foreground='#f39c12')  # 橙色
            self.status_text.config(text="处理中")

    def update_pc_count(self, count):
        self.pc_count = count
        self.pc_count_var.set(str(count))

    def update_enc_count(self, count):
        self.enc_count = count
        self.enc_count_var.set(str(count))

    def log_message(self, message, level='info'):
        timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
        level_prefix = ''
        text_color = ''

        # 根据日志级别设置前缀和颜色
        if level == 'error':
            level_prefix = '[错误] ' 
            text_color = '#e74c3c'
        elif level == 'warning':
            level_prefix = '[警告] ' 
            text_color = '#f39c12'
        elif level == 'info':
            level_prefix = '[信息] ' 
            text_color = '#2c3e50'
        elif level == 'success':
            level_prefix = '[成功] ' 
            text_color = '#27ae60'

        log_entry = f'{timestamp} - {level_prefix}{message}'
        self.log_text.insert(tk.END, log_entry + '\n')

        # 为不同级别的日志设置不同颜色
        last_line = self.log_text.index('end-2l')
        self.log_text.tag_add(level, last_line, f'{last_line} lineend')
        self.log_text.tag_config(level, foreground=text_color)

        self.log_text.see(tk.END)
        print(log_entry, end='\n')  # 添加控制台输出

    def update_runtime(self):
        elapsed = int(time.time() - self.start_time)
        hours, remainder = divmod(elapsed, 3600)
        minutes, seconds = divmod(remainder, 60)
        self.runtime_var.set(f'{hours:02d}:{minutes:02d}:{seconds:02d}')
        self.root.after(1000, self.update_runtime)

    def update_loop(self):
        """状态更新循环"""
        while True:
            try:
                # 这里可以添加其他需要定期更新的状态
                time.sleep(5)  # 每5秒更新一次
            except Exception as e:
                print(f"更新循环错误: {e}")
                time.sleep(5)

    def on_new_folder(self, folder_path, is_pc):
        def delayed_process():
            folder_name = os.path.basename(folder_path)
            # 生成5-10秒之间的随机延迟（以0.1秒为单位）
            delay_seconds = random.uniform(5.0, 10.0)
            self.log_message(f'检测到新文件夹 [{folder_name}]，将在 {delay_seconds:.1f} 秒后开始处理')
            time.sleep(delay_seconds)
            self.data_processor.process_folder(folder_path, is_pc)

        # 在新线程中处理文件夹，包含延迟
        threading.Thread(target=delayed_process).start()

    def start_monitoring(self):
        if not self.is_running:
            self.log_message('开始监控新文件...')

            # 检查监控目录是否存在
            pc_path = 'Z:\DATA_BAK\QAR_PC'
            enc_path = 'Z:\DATA_BAK\ENC_BAK'
            self.log_message(f'检查PC监控目录: {pc_path}')
            if os.path.exists(pc_path):
                self.log_message(f'PC监控目录存在: {pc_path}')
                self.log_message(f'PC目录文件数量: {len(os.listdir(pc_path))}')
            else:
                self.log_message(f'错误 - PC监控目录不存在: {pc_path}', level='error')

            self.log_message(f'检查ENC监控目录: {enc_path}')
            if os.path.exists(enc_path):
                self.log_message(f'ENC监控目录存在: {enc_path}')
                self.log_message(f'ENC目录文件数量: {len(os.listdir(enc_path))}')
            else:
                self.log_message(f'错误 - ENC监控目录不存在: {enc_path}', level='error')

            # 更新按钮状态（运行时禁用开始按钮，启用暂停按钮）
            self.run_button.config(state=tk.DISABLED)
            self.pause_button.config(state=tk.NORMAL)

            # 检查监控线程是否已启动，如果已启动则先停止
            if hasattr(self.pc_monitor, 'observer') and self.pc_monitor.observer.is_alive():
                self.pc_monitor.stop()

            if hasattr(self.enc_monitor, 'observer') and self.enc_monitor.observer.is_alive():
                self.enc_monitor.stop()

            # 重新创建监控器实例
            self.pc_monitor = FolderMonitor(pc_path, self.on_new_folder, is_pc=True)
            self.enc_monitor = FolderMonitor(enc_path, self.on_new_folder, is_pc=False)

            pc_started = self.pc_monitor.start()
            enc_started = self.enc_monitor.start()

            if pc_started and enc_started:
                self.is_running = True
                self.update_status('新文件持续监控中')
                self.log_message('监控已成功启动', level='success')

                # 扫描已有文件夹并处理
                self.scan_existing_folders()
            else:
                self.log_message('监控启动失败', level='error')
                if not pc_started:
                    self.log_message('错误 - 无法访问PC文件夹路径: Z:\DATA_BAK\QAR_PC', level='error')
                if not enc_started:
                    self.log_message('错误 - 无法访问ENC文件夹路径: Z:\DATA_BAK\ENC_BAK', level='error')

                # 恢复按钮状态
                self.run_button.config(state=tk.NORMAL)
                self.pause_button.config(state=tk.DISABLED)

    def scan_existing_folders(self):
        """扫描已有文件夹并处理符合条件的文件"""
        self.log_message('开始扫描已有文件夹...')
        last_run_timestamp = self.data_processor.get_last_run_timestamp()

        # 扫描PC目录
        pc_path = 'Z:\DATA_BAK\QAR_PC'
        if os.path.exists(pc_path):
            for folder_name in os.listdir(pc_path):
                folder_path = os.path.join(pc_path, folder_name)
                if os.path.isdir(folder_path):
                    try:
                        created_time = os.path.getctime(folder_path)
                        if created_time > last_run_timestamp or last_run_timestamp == 0:
                            self.log_message(f'发现未处理的PC文件夹: {folder_name}')
                            threading.Thread(target=self.data_processor.process_folder, args=(folder_path, True)).start()
                    except Exception as e:
                        self.log_message(f'扫描PC文件夹 {folder_name} 时出错: {str(e)}')

        # 扫描ENC目录
        enc_path = 'Z:\DATA_BAK\ENC_BAK'
        if os.path.exists(enc_path):
            for folder_name in os.listdir(enc_path):
                folder_path = os.path.join(enc_path, folder_name)
                if os.path.isdir(folder_path):
                    try:
                        created_time = os.path.getctime(folder_path)
                        if created_time > last_run_timestamp or last_run_timestamp == 0:
                            self.log_message(f'发现未处理的ENC文件夹: {folder_name}')
                            threading.Thread(target=self.data_processor.process_folder, args=(folder_path, False)).start()
                    except Exception as e:
                        self.log_message(f'扫描ENC文件夹 {folder_name} 时出错: {str(e)}')

        self.log_message('已有文件夹扫描完成')

    def pause_monitoring(self):
        if self.is_running:
            self.log_message('暂停监控...')
            self.pc_monitor.stop()
            self.enc_monitor.stop()
            self.is_running = False

            # 更新按钮状态（暂停时禁用暂停按钮，启用开始按钮）
            self.pause_button.config(state=tk.DISABLED)
            self.run_button.config(state=tk.NORMAL)

            # 更新上次停止时间
            current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            self.time_manager.set_last_run_time(current_time)
            self.last_stop_var.set(f'本次开始: {current_time}')

            self.update_status('监控已暂停')
            self.log_message('监控已暂停', level='warning')

    def start_schedule_thread(self):
        def run_schedule():
            while True:
                schedule.run_pending()
                time.sleep(1)

        schedule_thread = threading.Thread(target=run_schedule, daemon=True)
        schedule_thread.start()

    def exit_app(self):
        # 停止监控
        self.pause_monitoring()
        # 保存配置
        self.count_manager.set_pc_count(self.pc_count)
        self.count_manager.set_enc_count(self.enc_count)
        current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.time_manager.set_last_run_time(current_time)
        # 退出
        self.root.quit()
        self.root.destroy()

if __name__ == '__main__':
    root = tk.Tk()
    app = QARApp(root)
    root.mainloop()