import os
import subprocess
import time

# 测试Full_to_Daily.exe
print("=== 测试Full_to_Daily.exe ===")

exe_path = os.path.join(r"d:\auto_daily\QAR_Work\Vietnam\dist", "Full_to_Daily.exe")
print(f"检查EXE文件是否存在: {exe_path}")
if not os.path.exists(exe_path):
    print("错误: EXE文件不存在!")
else:
    print("EXE文件存在，准备运行...")
    
    # 创建测试目录
    test_dir = os.path.join(r"d:\auto_daily", "测试目录")
    os.makedirs(test_dir, exist_ok=True)
    print(f"创建测试目录: {test_dir}")
    
    # 运行EXE (注意: 这会打开GUI界面，用户需要手动选择文件)
    print("运行EXE文件...")
    print("请在弹出的窗口中选择一个测试Word文件")
    subprocess.Popen([exe_path])
    
    # 等待用户操作完成
    time.sleep(30)
    
    # 检查输出文件
    output_dir1 = os.path.join(r"d:\auto_daily", "越南湿租报告", "日报（王芃）")
    output_dir2 = os.path.join(r"d:\auto_daily", "越南湿租报告", "日报")
    
    print(f"检查王总版文件目录: {output_dir1}")
    if os.path.exists(output_dir1):
        files1 = os.listdir(output_dir1)
        print(f"目录中的文件: {files1}")
    else:
        print("王总版文件目录不存在!")
        
    print(f"检查最终报告目录: {output_dir2}")
    if os.path.exists(output_dir2):
        files2 = os.listdir(output_dir2)
        print(f"目录中的文件: {files2}")
    else:
        print("最终报告目录不存在!")
        
print("测试完成!")