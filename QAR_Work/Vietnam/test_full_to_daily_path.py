import os
import sys

sys.path.append('d:\auto_daily\QAR_Work\Vietnam')
import Full_to_Daily as ftd

# 测试路径设置
print("=== 测试Full_to_Daily.py中的路径设置 ===")

# 测试select_source_files函数
print("\n测试select_source_files函数...")
# 注意：这个函数会打开文件选择对话框，我们无法在这里直接测试
print("提示: select_source_files函数会打开文件选择对话框，使用d:\auto_daily\越南湿租报告\日报（卿光宇）作为默认目录")

# 测试get_template_path函数
print("\n测试get_template_path函数...")
template_path = ftd.get_template_path()
print(f"模板文件路径: {template_path}")
print(f"模板文件是否存在: {os.path.exists(template_path)}")

# 测试create_wangpeng_version函数
print("\n测试create_wangpeng_version函数...")
# 这里需要一个测试Word文件，假设我们使用模板文件作为测试
if os.path.exists(template_path):
    wp_path, date_part, wp_doc = ftd.create_wangpeng_version(template_path)
    print(f"王总版文件路径: {wp_path}")
    print(f"王总版文件是否存在: {os.path.exists(wp_path)}")

    # 测试extract_parameters函数
    print("\n测试extract_parameters函数...")
    parameters = ftd.extract_parameters(wp_doc)
    print("提取的参数:")
    for key, value in parameters.items():
        print(f"  {key}: {value}")

    # 测试create_final_report函数
    print("\n测试create_final_report函数...")
    report_path = ftd.create_final_report(template_path, date_part, parameters, wp_path)
    print(f"最终报告文件路径: {report_path}")
    print(f"最终报告文件是否存在: {os.path.exists(report_path)}")
else:
    print("模板文件不存在，无法测试create_wangpeng_version函数")

print("\n=== 测试完成 ===")