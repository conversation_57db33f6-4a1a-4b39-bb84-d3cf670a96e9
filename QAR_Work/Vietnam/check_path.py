import os

# 检查路径设置
print("=== 检查Full_to_Daily.py中的路径设置 ===")

# 检查d:\auto_daily目录是否存在
daily_dir = r'd:\auto_daily'
print(f"检查{daily_dir}目录是否存在: {os.path.exists(daily_dir)}")

# 检查越南湿租报告目录是否存在
vietnam_dir = os.path.join(daily_dir, '越南湿租报告')
print(f"检查{vietnam_dir}目录是否存在: {os.path.exists(vietnam_dir)}")

# 检查日报（王芃）目录是否存在
wp_dir = os.path.join(vietnam_dir, '日报（王芃）')
print(f"检查{wp_dir}目录是否存在: {os.path.exists(wp_dir)}")

# 检查日报目录是否存在
report_dir = os.path.join(vietnam_dir, '日报')
print(f"检查{report_dir}目录是否存在: {os.path.exists(report_dir)}")

# 检查Template目录是否存在
template_dir = os.path.join(daily_dir, 'Template')
print(f"检查{template_dir}目录是否存在: {os.path.exists(template_dir)}")

# 检查模板文件是否存在
template_file = os.path.join(template_dir, 'C909越南运行QAR日报(模板).docx')
print(f"检查{template_file}文件是否存在: {os.path.exists(template_file)}")

print("=== 检查完成 ===")