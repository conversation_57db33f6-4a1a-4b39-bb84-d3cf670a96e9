import os
import sys
import traceback

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

# 尝试导入Daily_to_Excel.py中的关键函数
try:
    # 模拟修改后的路径设置
    current_dir = r'd:\auto_daily'
    word_dir = os.path.join(current_dir, '越南湿租报告', '日报（王芃）')
    template_dir = os.path.join(current_dir, 'Template')
    template_path = os.path.join(template_dir, 'Vietnam_Template.xlsx')
    output_path = os.path.join(current_dir, '越南湿租报告', 'Vietnam_Report.xlsx')

    # 打印路径信息
    print(f"基准目录: {current_dir}")
    print(f"Word文件目录: {word_dir}")
    print(f"模板文件目录: {template_dir}")
    print(f"模板文件路径: {template_path}")
    print(f"输出文件路径: {output_path}")

    # 检查路径是否存在
    print("\n路径检查结果:")
    paths_to_check = [
        (current_dir, "基准目录"),
        (word_dir, "Word文件目录"),
        (template_dir, "模板文件目录"),
        (template_path, "模板文件"),
        (os.path.dirname(output_path), "输出文件目录")
    ]

    all_paths_valid = True
    for path, description in paths_to_check:
        if os.path.exists(path):
            print(f"✓ {description} 存在: {path}")
            # 如果是模板目录，列出其中的文件
            if description == "模板文件目录":
                try:
                    files = os.listdir(path)
                    if files:
                        print(f"  模板目录中的文件: {', '.join(files)}")
                    else:
                        print(f"  模板目录为空")
                except Exception as e:
                    print(f"  无法列出模板目录内容: {str(e)}")
        else:
            print(f"✗ {description} 不存在: {path}")
            all_paths_valid = False

    # 检查是否有权限写入输出目录
    if all_paths_valid:
        test_file = os.path.join(os.path.dirname(output_path), 'test_write_permission.txt')
        try:
            with open(test_file, 'w') as f:
                f.write('测试写入权限')
            os.remove(test_file)
            print(f"✓ 输出目录有写入权限")
        except Exception as e:
            print(f"✗ 输出目录无写入权限: {str(e)}")
            all_paths_valid = False

    # 检查是否有Word文件
    if all_paths_valid and os.path.exists(word_dir):
        word_files = [f for f in os.listdir(word_dir) if f.endswith('.docx')]
        if word_files:
            print(f"✓ 找到 {len(word_files)} 个Word文件")
            print(f"  示例: {word_files[0]}")
        else:
            print(f"✗ Word文件目录中未找到.docx文件")

    print("\n测试完成。")
    if all_paths_valid:
        print("结论: 路径设置正确，所有必要的目录和文件都存在。")
    else:
        print("结论: 路径设置存在问题，请检查上述报告的缺失项。")

except Exception as e:
    print(f"测试过程中出错: {str(e)}")
    print(traceback.format_exc())