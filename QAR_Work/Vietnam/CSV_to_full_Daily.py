import pandas as pd
from openpyxl import load_workbook
from openpyxl.styles import Alignment
import os
import tkinter as tk
from tkinter import simpledialog
import sys
from docx import Document
from datetime import datetime, time
from docx.shared import Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml.ns import qn
from docx.oxml import OxmlElement
from docx.shared import RGBColor

# 全局路径设置
CURRENT_DIR = 'd:\\auto_daily'
TEMPLATE_DIR = os.path.join(CURRENT_DIR, "Template")
PARAMETER_DIR = os.path.join(CURRENT_DIR, "Parameter")
REPORT_DIR = os.path.join(CURRENT_DIR, "越南湿租报告", "日报（卿光宇）")
os.makedirs(REPORT_DIR, exist_ok=True)  # 确保目录存在
BASE_TEMPLATE_NAME = "C909越南运行QAR日报_full(模板)"
REPORT_NAME_PREFIX = "C909越南运行QAR日报_卿总"

# 自动读取的CSV文件路径
AUTO_CSV_PATH = r"\\10.2.3.172\qar_nas\Vietnam\Reports\ARJ21_Vietnam.csv"


def format_numeric_value(value):
    """格式化数值：如果是浮点数且为整数则去掉小数点"""
    try:
        num = float(value)
        if num.is_integer():
            return str(int(num))
        return str(num)
    except (ValueError, TypeError):
        return str(value)


def remove_duplicate_rows(df):
    """删除重复行：关键字段相同且起飞时间相差5分钟内的行"""
    if len(df) == 0:
        return df

    # 确保必要的列存在
    required_columns = ['飞机号', '起飞机场', '着陆机场', '起飞日期', '起飞时间']
    for col in required_columns:
        if col not in df.columns:
            print(f"警告：缺少必要列'{col}'，跳过查重步骤")
            return df

    try:
        # 转换日期和时间为可比较的格式
        df['起飞日期'] = pd.to_datetime(df['起飞日期']).dt.date
        # 明确指定时间格式为HH:MM:SS
        df['起飞时间'] = pd.to_datetime(df['起飞时间'], format='%H:%M:%S').dt.time

        # 创建合并的datetime列用于比较
        df['起飞日期时间'] = df.apply(
            lambda x: datetime.combine(x['起飞日期'], x['起飞时间']),
            axis=1
        )

        # 标记要删除的行
        to_drop = []
        prev_row = None

        for i, current_row in df.iterrows():
            if prev_row is not None:
                # 检查关键字段是否相同
                same_key_fields = (
                        current_row['飞机号'] == prev_row['飞机号'] and
                        current_row['起飞机场'] == prev_row['起飞机场'] and
                        current_row['着陆机场'] == prev_row['着陆机场'] and
                        current_row['起飞日期'] == prev_row['起飞日期']
                )

                # 检查时间差是否在5分钟内
                time_diff = abs((current_row['起飞日期时间'] - prev_row['起飞日期时间']).total_seconds() / 60)
                within_5min = time_diff <= 5

                if same_key_fields and within_5min:
                    to_drop.append(i)
                else:
                    prev_row = current_row
            else:
                prev_row = current_row

        # 删除标记的行
        if to_drop:
            print(f"\n发现并删除{len(to_drop)}行重复数据")
            df = df.drop(to_drop)

        # 删除临时列并重置索引
        df = df.drop(columns=['起飞日期时间'])
        return df.reset_index(drop=True)

    except ValueError as e:
        print(f"\n时间格式解析错误，请确保'起飞时间'列为HH:MM:SS格式: {str(e)}")
        return df
    except Exception as e:
        print(f"\n查重时发生错误: {str(e)}")
        return df


def select_and_process_files():
    """代码1的主要功能：处理CSV文件并生成Excel"""
    # 使用自动路径而不是手动选择
    csv_file = AUTO_CSV_PATH

    if not os.path.exists(csv_file):
        print(f"错误：找不到CSV文件 {csv_file}")
        sys.exit(1)

    # 自动生成关联文件名（输出到当前目录）
    base_name = os.path.splitext(os.path.basename(csv_file))[0]
    dict_file = os.path.join(PARAMETER_DIR, f"Parm_{base_name}.xlsx")
    output_file = os.path.join(CURRENT_DIR, f"{base_name}.xlsx")

    # 验证字典文件存在
    if not os.path.exists(dict_file):
        print(f"错误：找不到字典文件 {dict_file}")
        sys.exit(1)

    # 静默处理文件
    print("\n正在处理文件...")
    print(f"原始数据: {csv_file}")
    print(f"字典文件: {dict_file}")
    print(f"输出文件: {output_file}")

    try:
        # ===== 1. 数据读取和基础处理 =====
        dict_df = pd.read_excel(dict_file)
        param_dict = dict(zip(dict_df['序号'], dict_df['中文名']))
        data_df = pd.read_csv(csv_file, delimiter=';', header=None)
        data_df.columns = [param_dict.get(col + 1, f'参数{col + 1}') for col in range(len(data_df.columns))]

        # ===== 2. 数据排序处理 =====
        # 检查排序列是否存在
        sort_columns = []
        if '起飞日期' in data_df.columns:
            sort_columns.append(('起飞日期', False))  # 降序
        if '飞机号' in data_df.columns:
            sort_columns.append(('飞机号', True))  # 升序
        if '起飞时间' in data_df.columns:
            sort_columns.append(('起飞时间', False))  # 降序

        if sort_columns:
            print("\n正在执行排序...")
            sort_order = [f"{col}({'升序' if asc else '降序'})" for col, asc in sort_columns]
            print(f"排序顺序: {', '.join(sort_order)}")

            data_df.sort_values(
                by=[col for col, asc in sort_columns],
                ascending=[asc for col, asc in sort_columns],
                inplace=True
            )
        else:
            print("\n警告：未找到排序列，跳过排序步骤")

        # ===== 3. 数据去重处理 =====
        print("\n正在执行查重...")
        data_df = remove_duplicate_rows(data_df)

        # ===== 4. Excel格式处理 =====
        temp_file = os.path.join(CURRENT_DIR, "temp.xlsx")
        with pd.ExcelWriter(temp_file, engine='openpyxl') as writer:
            data_df.to_excel(writer, index=False)
            ws = writer.sheets['Sheet1']

            # 格式设置
            ws.freeze_panes = 'A2'
            for row in ws.iter_rows():
                for cell in row:
                    cell.alignment = Alignment(
                        horizontal='center',
                        vertical='center',
                        wrap_text=False
                    )

        # ===== 5. 列宽自动调整 =====
        wb = load_workbook(temp_file)
        ws = wb.active

        for col in ws.columns:
            max_len = max(
                sum(2 if ord(c) > 127 else 1 for c in str(cell.value))
                for cell in col if cell.value
            ) or 10  # 默认最小宽度
            ws.column_dimensions[col[0].column_letter].width = max_len + 1

        # ===== 6. 最终保存 =====
        wb.save(output_file)
        os.remove(temp_file)

        print(f"\n处理完成！结果已保存到:\n{output_file}")
        return output_file

    except Exception as e:
        print(f"\n处理失败: {str(e)}")
        temp_file = os.path.join(CURRENT_DIR, "temp.xlsx")
        if os.path.exists(temp_file):
            os.remove(temp_file)
        sys.exit(1)

def set_cell_format(cell):
    """设置单元格格式：仿宋_GB2312、14磅、居中，并取消对齐网格"""
    for paragraph in cell.paragraphs:
        # 取消"对齐到网格"设置
        pPr = paragraph._p.get_or_add_pPr()
        snapToGrid = OxmlElement('w:snapToGrid')
        snapToGrid.set(qn('w:val'), '0')
        pPr.append(snapToGrid)

        paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        for run in paragraph.runs:
            run.font.name = '仿宋_GB2312'
            run._element.rPr.rFonts.set(qn('w:eastAsia'), '仿宋_GB2312')
            run.font.size = Pt(14)


def update_first_page(doc, params):
    """高效替换所有参数"""
    for paragraph in doc.paragraphs:
        full_text = ''.join(run.text for run in paragraph.runs)
        if any(f"({param})" in full_text for param in params):
            # 保存格式
            fmt = {
                'font': paragraph.runs[0].font.name,
                'size': paragraph.runs[0].font.size,
                'bold': paragraph.runs[0].font.bold
            } if paragraph.runs else None

            # 执行替换
            new_text = full_text
            for param, value in params.items():
                new_text = new_text.replace(f"({param})", str(value))

            # 更新内容
            for run in paragraph.runs:
                run.text = ""
            if fmt:
                run = paragraph.add_run(new_text)
                run.font.name = fmt['font']
                run._element.rPr.rFonts.set(qn('w:eastAsia'), fmt['font'])
                run.font.size = fmt['size']
                run.font.bold = fmt['bold']


def select_template(b652g_flt, b656e_flt):
    """根据航班数量选择合适的模板"""
    # 定义模板选择规则
    if b652g_flt >= 6 and b656e_flt >= 6:
        template_name = f"{BASE_TEMPLATE_NAME}_G6E6.docx"
    elif b652g_flt >= 6 and b656e_flt == 5:
        template_name = f"{BASE_TEMPLATE_NAME}_G6E5.docx"
    elif b652g_flt >= 6 and b656e_flt <= 4:
        template_name = f"{BASE_TEMPLATE_NAME}_G6.docx"
    elif b652g_flt == 5 and b656e_flt >= 6:
        template_name = f"{BASE_TEMPLATE_NAME}_G5E6.docx"
    elif b652g_flt == 5 and b656e_flt == 5:
        template_name = f"{BASE_TEMPLATE_NAME}_G5E5.docx"
    elif b652g_flt == 5 and b656e_flt <= 4:
        template_name = f"{BASE_TEMPLATE_NAME}_G5.docx"
    elif b652g_flt <= 4 and b656e_flt >= 6:
        template_name = f"{BASE_TEMPLATE_NAME}_E6.docx"
    elif b652g_flt <= 4 and b656e_flt == 5:
        template_name = f"{BASE_TEMPLATE_NAME}_E5.docx"
    else:  # b652g_flt <=4 and b656e_flt <=4
        template_name = f"{BASE_TEMPLATE_NAME}.docx"

    return os.path.join(TEMPLATE_DIR, template_name)


def check_template_files():
    """检查所有模板文件是否存在"""
    required_templates = [
        f"{BASE_TEMPLATE_NAME}_G6E6.docx",
        f"{BASE_TEMPLATE_NAME}_G6E5.docx",
        f"{BASE_TEMPLATE_NAME}_G6.docx",
        f"{BASE_TEMPLATE_NAME}_G5E6.docx",
        f"{BASE_TEMPLATE_NAME}_G5E5.docx",
        f"{BASE_TEMPLATE_NAME}_E6.docx",
        f"{BASE_TEMPLATE_NAME}_E5.docx",
        f"{BASE_TEMPLATE_NAME}.docx"
    ]

    missing_files = []
    for template in required_templates:
        template_path = os.path.join(TEMPLATE_DIR, template)
        if not os.path.exists(template_path):
            missing_files.append(template)

    if missing_files:
        raise FileNotFoundError(
            f"以下模板文件在{TEMPLATE_DIR}目录中缺失:\n" +
            "\n".join(missing_files)
        )


def fill_word_template(excel_path, report_date):
    """代码2的主要功能：根据Excel生成Word报告"""
    # 检查模板文件
    check_template_files()

    # 读取并预处理数据
    df = pd.read_excel(excel_path)
    df['起飞日期'] = pd.to_datetime(df['起飞日期']).dt.date
    report_date_dt = datetime.strptime(report_date, '%Y-%m-%d').date()
    daily_data = df[df['起飞日期'] == report_date_dt].sort_values('起飞时间')

    # 计算关键参数
    b652g_flt = len(daily_data[daily_data['飞机号'] == 'B-652G'])
    b656e_flt = len(daily_data[daily_data['飞机号'] == 'B-656E'])

    params = {
        'month': report_date_dt.month,
        'day': report_date_dt.day,
        'AC_Num': len(daily_data['飞机号'].unique()),
        'Flight_Num': len(daily_data),
        'WQAR_Num': len(daily_data),
        'B_652G_Flt': b652g_flt,
        'B_656E_Flt': b656e_flt
    }

    # 打印参数值
    print("\n=== 飞行数据统计 ===")
    for k, v in params.items():
        print(f"{k}: {v}")

    # 选择模板文件
    template_file = select_template(b652g_flt, b656e_flt)
    print(f"\n使用的模板文件: {template_file}")

    # 处理Word文档
    doc = Document(template_file)
    update_first_page(doc, params)

    # 定义表格字段映射
    field_mapping = {
        'takeoff': [
            ("航班号", "航班号"),
            ("起降机场", lambda r: f"{r['起飞机场']}-{r['着陆机场']}"),
            ("起飞时间(北京时间)", "起飞时间"),
            ("起飞重量(吨)", "起飞重量"),
            ("起飞燃油（吨）", "起飞燃油"),
            ("平均顺风(节)", "起飞平均顺风"),
            ("最大侧风(节)", "起飞最大侧风"),
            ("起飞滑跑距离(米)", "起飞滑跑距离"),
            ("抬轮与VR时差(秒)", "抬轮与VR时差"),
            ("离地姿态(度)", "离地姿态"),
            ("抬轮最大速率(度/秒)", "抬轮最大速率"),
            ("抬轮平均速率(度/秒)", "抬轮平均速率"),
            ("滑跑航向偏离(度)", "起飞滑跑航向偏离"),
            ("最大坡度(度)", "起飞最大坡度")
        ],
        'landing': [
            ("航班号", "航班号"),
            ("起降机场", lambda r: f"{r['起飞机场']}-{r['着陆机场']}"),
            ("着陆时间(北京时间)", "落地时间"),
            ("着陆重量(吨)", "着陆重量"),
            ("着陆剩余燃油(吨)", "着陆剩余燃油"),
            ("着陆构型", "着陆构型"),
            ("平均顺风(节)", "着陆平均顺风"),
            ("最大侧风(节)", "着陆最大侧风"),
            ("稳定进近高度(英尺)", "稳定进近高度"),
            ("进跑道高度(英尺)", "进跑道高度"),
            ("拉开始高度(英尺)", "拉开始高度"),
            ("接近拉平高度（英尺）", "接近拉平高度"),
            ("接地点距离(米)", "接地点距离"),
            ("入口至减速到40节距离(米)", "入口至减速到40节距离"),
            ("进近速度偏差", "进近速度偏差"),
            ("着陆姿态(度)", "着陆姿态"),
            ("着陆载荷(G）", "着陆载荷"),
            ("着陆坡度(度)", "着陆坡度"),
            ("滑跑航向偏离(度)", "滑跑航向偏离"),
            ("收光油门时机", "收光油门时机"),
            ("开反推时间(秒)", "开反推时间"),
            ("反推使用时长(秒)", "反推使用时长")
        ]
    }

    # 填充表格数据
    table_mapping = {
        0: ('B-652G', 'takeoff'),
        1: ('B-656E', 'takeoff'),
        2: ('B-652G', 'landing'),
        3: ('B-656E', 'landing')
    }

    for table_idx, (plane, data_type) in table_mapping.items():
        table = doc.tables[table_idx]
        plane_data = daily_data[daily_data['飞机号'] == plane]

        for col_idx, (_, row) in enumerate(plane_data.iterrows(), 3):
            while len(table.columns) <= col_idx:
                table.add_column()
            for row_idx, (_, field) in enumerate(field_mapping[data_type]):
                if row_idx < len(table.rows) and col_idx < len(table.rows[row_idx].cells):
                    value = field(row) if callable(field) else row.get(field, '')
                    # 添加数值格式化处理
                    formatted_value = format_numeric_value(value)
                    table.cell(row_idx, col_idx).text = formatted_value
                    set_cell_format(table.cell(row_idx, col_idx))

    # 新增：标注偏差数据
    highlight_deviations(doc, daily_data)

    # 保存文件
    output_file = os.path.join(
        REPORT_DIR,
        f"C909越南运行QAR日报_卿总_{report_date_dt.strftime('%Y%m%d')}.docx"
    )
    doc.save(output_file)
    return output_file


def highlight_deviations(doc, daily_data):
    """根据规则标注偏差数据为红色"""
    red_color = RGBColor(255, 0, 0)  # 红色

    # 处理起飞表格
    for table_idx in [0, 1]:  # 0是B-652G起飞表，1是B-656E起飞表
        table = doc.tables[table_idx]
        plane = 'B-652G' if table_idx == 0 else 'B-656E'
        plane_data = daily_data[daily_data['飞机号'] == plane]

        for col_idx, (_, row) in enumerate(plane_data.iterrows(), 3):
            # 确保列存在
            while col_idx >= len(table.columns):
                table.add_column()

            # 第9行: 抬轮与VR时差(秒) >=3
            if float(row.get('抬轮与VR时差', 0)) >= 3:
                cell = table.cell(8, col_idx)
                for paragraph in cell.paragraphs:
                    for run in paragraph.runs:
                        run.font.color.rgb = red_color

            # 第10行: 离地姿态(度) >=8.5或<=5.5
            attitude = float(row.get('离地姿态', 0))
            if attitude >= 8.5 or attitude <= 5.5:
                cell = table.cell(9, col_idx)
                for paragraph in cell.paragraphs:
                    for run in paragraph.runs:
                        run.font.color.rgb = red_color

            # 第11行: 抬轮最大速率(度/秒) >=4.5
            if float(row.get('抬轮最大速率', 0)) >= 4.5:
                cell = table.cell(10, col_idx)
                for paragraph in cell.paragraphs:
                    for run in paragraph.runs:
                        run.font.color.rgb = red_color

            # 第12行: 抬轮平均速率(度/秒) >=4
            if float(row.get('抬轮平均速率', 0)) >= 4:
                cell = table.cell(11, col_idx)
                for paragraph in cell.paragraphs:
                    for run in paragraph.runs:
                        run.font.color.rgb = red_color

            # 第13行: 滑跑航向偏离(度) >=4
            if abs(float(row.get('起飞滑跑航向偏离', 0))) >= 4:
                cell = table.cell(12, col_idx)
                for paragraph in cell.paragraphs:
                    for run in paragraph.runs:
                        run.font.color.rgb = red_color

            # 第14行: 最大坡度(度) >=4
            if abs(float(row.get('起飞最大坡度', 0))) >= 4:
                cell = table.cell(13, col_idx)
                for paragraph in cell.paragraphs:
                    for run in paragraph.runs:
                        run.font.color.rgb = red_color

    # 处理着陆表格
    for table_idx in [2, 3]:  # 2是B-652G着陆表，3是B-656E着陆表
        table = doc.tables[table_idx]
        plane = 'B-652G' if table_idx == 2 else 'B-656E'
        plane_data = daily_data[daily_data['飞机号'] == plane]

        for col_idx, (_, row) in enumerate(plane_data.iterrows(), 3):
            # 确保列存在
            while col_idx >= len(table.columns):
                table.add_column()

            # 第17行: 着陆载荷(G) >1.6 (行索引16)
            if float(row.get('着陆载荷', 0)) > 1.6:
                cell = table.cell(16, col_idx)
                for paragraph in cell.paragraphs:
                    for run in paragraph.runs:
                        run.font.color.rgb = red_color

            # 检查第二行最右边4个字符是否为"VVCS"
            airport_pair = row.get('起飞机场', '') + '-' + row.get('着陆机场', '')
            if airport_pair[-4:] == "VVCS":
                # 修正后的行号对应关系：
                # 第10行(索引9): 进跑道高度(英尺) >=55或<=40
                height = float(row.get('进跑道高度', 0))
                if height >= 55 or height <= 40:
                    cell = table.cell(9, col_idx)
                    for paragraph in cell.paragraphs:
                        for run in paragraph.runs:
                            run.font.color.rgb = red_color

                # 第11行(索引10): 拉开始高度(英尺) >=34或<=20
                height = float(row.get('拉开始高度', 0))
                if height >= 35 or height <= 18:
                    cell = table.cell(10, col_idx)
                    for paragraph in cell.paragraphs:
                        for run in paragraph.runs:
                            run.font.color.rgb = red_color

                # 第12行(索引11): 接近拉平高度(英尺) >=23或<=9
                height = float(row.get('接近拉平高度', 0))
                if height >= 23 or height <= 8:
                    cell = table.cell(11, col_idx)
                    for paragraph in cell.paragraphs:
                        for run in paragraph.runs:
                            run.font.color.rgb = red_color

                # 第13行(索引12): 接地点距离(米) >=550或<=300
                distance = float(row.get('接地点距离', 0))
                if distance >= 550 or distance <= 300:
                    cell = table.cell(12, col_idx)
                    for paragraph in cell.paragraphs:
                        for run in paragraph.runs:
                            run.font.color.rgb = red_color

                # 第14行(索引13): 入口至减速到40节距离(米) >=1600
                if float(row.get('入口至减速到40节距离', 0)) >= 1600:
                    cell = table.cell(13, col_idx)
                    for paragraph in cell.paragraphs:
                        for run in paragraph.runs:
                            run.font.color.rgb = red_color

                # 第15行(索引14): 进近速度偏差 >=5
                if abs(float(row.get('进近速度偏差', 0))) >= 5:
                    cell = table.cell(14, col_idx)
                    for paragraph in cell.paragraphs:
                        for run in paragraph.runs:
                            run.font.color.rgb = red_color

                # 第16行(索引15): 着陆姿态(度) >=6或<=2
                attitude = float(row.get('着陆姿态', 0))
                if attitude >= 6 or attitude <= 2:
                    cell = table.cell(15, col_idx)
                    for paragraph in cell.paragraphs:
                        for run in paragraph.runs:
                            run.font.color.rgb = red_color

                # 第18行(索引17): 着陆坡度(度) >=4
                if abs(float(row.get('着陆坡度', 0))) >= 4:
                    cell = table.cell(17, col_idx)
                    for paragraph in cell.paragraphs:
                        for run in paragraph.runs:
                            run.font.color.rgb = red_color

                # 第19行(索引18): 滑跑航向偏离(度) >=4
                if abs(float(row.get('滑跑航向偏离', 0))) >= 4:
                    cell = table.cell(18, col_idx)
                    for paragraph in cell.paragraphs:
                        for run in paragraph.runs:
                            run.font.color.rgb = red_color

                # 第20行(索引19): 收光油门时机 >=20或<=5
                timing = float(row.get('收光油门时机', 0))
                if timing >= 20 or timing <= 5:
                    cell = table.cell(19, col_idx)
                    for paragraph in cell.paragraphs:
                        for run in paragraph.runs:
                            run.font.color.rgb = red_color

                # 第21行(索引20): 开反推时间(秒) >=3
                if float(row.get('开反推时间', 0)) >= 3:
                    cell = table.cell(20, col_idx)
                    for paragraph in cell.paragraphs:
                        for run in paragraph.runs:
                            run.font.color.rgb = red_color

                # 第22行(索引21): 反推使用时长(秒) <=7
                if float(row.get('反推使用时长', 0)) <= 7:
                    cell = table.cell(21, col_idx)
                    for paragraph in cell.paragraphs:
                        for run in paragraph.runs:
                            run.font.color.rgb = red_color

def main():
    """主程序流程"""
    print("=== CSV转Excel转换器（带排序功能） ===")
    # 第一步：处理CSV文件生成Excel
    excel_file = select_and_process_files()

    # 第二步：生成Word报告
    root = tk.Tk()
    root.withdraw()
    report_date = simpledialog.askstring(
        "输入日期", "请输入日期(YYYY-MM-DD):",
        initialvalue=datetime.today().strftime('%Y-%m-%d')
    )

    if not report_date:
        print("未输入日期")
        return

    try:
        datetime.strptime(report_date, '%Y-%m-%d')
    except ValueError:
        print("日期格式不正确，请使用YYYY-MM-DD格式")
        return

    # 检查模板目录是否存在
    if not os.path.exists(TEMPLATE_DIR):
        print(f"模板目录不存在: {TEMPLATE_DIR}")
        return

    try:
        output = fill_word_template(excel_file, report_date)
        print(f"\n生成成功: {output}")
    except Exception as e:
        print(f"\n生成失败: {str(e)}")

if __name__ == "__main__":
    main()