import os
import sys
import traceback
from datetime import datetime, timedelta

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

# 尝试导入Daily_to_Excel.py中的关键函数
from Daily_to_Excel import process_word_file, save_workbook, format_excel
import openpyxl
import shutil

try:
    # 设置测试参数
    test_date = datetime.now().date() - timedelta(days=1)
    date_str = test_date.strftime("%Y%m%d")

    # 使用固定路径作为基准路径
    current_dir = r'd:\auto_daily'
    word_dir = os.path.join(current_dir, '越南湿租报告', '日报（王芃）')
    template_dir = os.path.join(current_dir, 'Template')
    template_path = os.path.join(template_dir, 'Vietnam_Template.xlsx')
    output_path = os.path.join(current_dir, '越南湿租报告', 'Vietnam_Report_Test.xlsx')

    # 打印路径信息
    print(f"基准目录: {current_dir}")
    print(f"Word文件目录: {word_dir}")
    print(f"模板文件路径: {template_path}")
    print(f"输出文件路径: {output_path}")

    # 检查路径是否存在
    print("\n路径检查结果:")
    paths_to_check = [
        (current_dir, "基准目录"),
        (word_dir, "Word文件目录"),
        (template_path, "模板文件")
    ]

    all_paths_valid = True
    for path, description in paths_to_check:
        if os.path.exists(path):
            print(f"✓ {description} 存在: {path}")
        else:
            print(f"✗ {description} 不存在: {path}")
            all_paths_valid = False

    if not all_paths_valid:
        print("\n结论: 路径设置存在问题，测试终止。")
        exit(1)

    # 查找测试Word文件
    word_file = f"C909越南运行QAR日报_王总_{date_str}.docx"
    word_path = os.path.join(word_dir, word_file)

    if not os.path.exists(word_path):
        # 尝试查找最近的Word文件
        word_files = [f for f in os.listdir(word_dir) if f.endswith('.docx')]
        if not word_files:
            print(f"✗ 错误: Word文件目录中未找到任何.docx文件")
            exit(1)
        else:
            word_file = word_files[-1]
            word_path = os.path.join(word_dir, word_file)
            print(f"✓ 找到最近的Word文件: {word_file}")
    else:
        print(f"✓ 找到测试Word文件: {word_file}")

    # 准备输出文件
    if os.path.exists(output_path):
        os.remove(output_path)
        print(f"✓ 删除旧的测试输出文件")

    shutil.copy(template_path, output_path)
    print(f"✓ 复制模板文件到输出路径")

    # 处理Word文件
    print(f"\n开始处理Word文件: {word_file}")
    process_word_file(word_path, output_path)
    print(f"✓ 成功处理Word文件")

    # 检查输出文件是否生成
    if os.path.exists(output_path):
        print(f"\n✓ 成功生成输出文件: {output_path}")
        file_size = os.path.getsize(output_path) / 1024
        print(f"  文件大小: {file_size:.2f} KB")
        print("结论: Daily_to_Excel.py路径设置正确，功能正常。")
    else:
        print(f"\n✗ 错误: 未生成输出文件")
        print("结论: Daily_to_Excel.py运行失败，可能存在其他问题。")

except Exception as e:
    print(f"测试过程中出错: {str(e)}")
    print(traceback.format_exc())
    exit(1)

print("\n测试完成。")