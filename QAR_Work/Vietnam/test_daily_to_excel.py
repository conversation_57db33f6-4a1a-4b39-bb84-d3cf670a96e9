import os
import subprocess
import time
import shutil
from datetime import datetime, timedelta

# 检查EXE文件是否存在
current_dir = r'd:\auto_daily\QAR_Work\Vietnam'
exe_path = os.path.join(current_dir, 'dist', 'Daily_to_Excel.exe')

if not os.path.exists(exe_path):
    print(f"✗ 错误: 未找到Daily_to_Excel.exe文件，请确保已成功生成。")
    exit(1)

print(f"✓ 找到Daily_to_Excel.exe文件: {exe_path}")

# 创建测试目录
output_dir = os.path.join(r'd:\auto_daily', '越南湿租报告')
output_file = os.path.join(output_dir, 'Vietnam_Report.xlsx')

try:
    # 删除旧的输出文件
    if os.path.exists(output_file):
        os.remove(output_file)
        print(f"✓ 删除旧的输出文件: {output_file}")

    # 运行EXE文件
    print("\n开始运行Daily_to_Excel.exe...")
    process = subprocess.Popen([exe_path], shell=True)

    # 给用户时间进行操作
    print("请在弹出的窗口中选择日期范围，然后点击'开始处理'按钮。")
    print("处理完成后，请关闭窗口以继续测试。")
    process.wait()

    # 检查输出文件是否生成
    if os.path.exists(output_file):
        print(f"\n✓ 成功生成输出文件: {output_file}")
        file_size = os.path.getsize(output_file) / 1024
        print(f"  文件大小: {file_size:.2f} KB")
        print("结论: Daily_to_Excel.exe运行正常，路径设置正确。")
    else:
        print(f"\n✗ 错误: 未生成输出文件: {output_file}")
        print("结论: Daily_to_Excel.exe运行失败，可能存在其他问题。")

except Exception as e:
    print(f"测试过程中出错: {str(e)}")
    exit(1)

print("\n测试完成。")