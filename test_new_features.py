#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新增功能：
1. 每日0点清理成功条目
2. 各阶段进度跟踪
3. 文件压缩进度显示
"""

import os
import sys
import tempfile
import threading
import time
from datetime import datetime, timedelta

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入主程序模块
from QAR_Work.PC_AUTO_BAK.PC_AUTO_BAK import PCAutoUnzipGUI, setup_logging

class MockGUI:
    """模拟GUI类，用于测试进度跟踪功能"""
    
    def __init__(self):
        self.current_step_text = ""
        self.step_data = {1: [], 2: [], 3: [], 4: [], 5: [], 6: []}
        self.cleanup_timer = None
        
    def update_current_step(self, step_text, current_item=None, progress_info=None):
        """更新当前处理步骤显示"""
        display_text = step_text
        if current_item:
            display_text += f" - 正在处理: {current_item}"
        if progress_info:
            display_text += f" ({progress_info})"
        
        self.current_step_text = display_text
        print(f"[进度] {display_text}")
        
    def add_step_result(self, step_num, **kwargs):
        """添加步骤结果"""
        result_data = {
            'values': [
                kwargs.get('data_name', ''),
                kwargs.get('mcc_aircraft', ''),
                kwargs.get('mcc_date', ''),
                kwargs.get('msg_aircraft', ''),
                kwargs.get('msg_date', ''),
                kwargs.get('bak_aircraft', ''),
                kwargs.get('bak_date', ''),
                kwargs.get('result', '')
            ]
        }
        self.step_data[step_num].append(result_data)
        print(f"[结果] 步骤{step_num}: {kwargs.get('data_name', '')} - {kwargs.get('result', '')}")
        
    def switch_to_step(self, step_num):
        """切换到指定步骤"""
        print(f"[切换] 切换到步骤 {step_num}")
        
    def schedule_daily_cleanup(self):
        """安排每日清理任务（测试版本）"""
        print("[清理] 安排每日清理任务")
        # 测试版本：5秒后执行清理
        self.cleanup_timer = threading.Timer(5.0, self.daily_cleanup_task)
        self.cleanup_timer.daemon = True
        self.cleanup_timer.start()
        
    def daily_cleanup_task(self):
        """每日清理任务"""
        print("[清理] 开始执行每日清理任务")
        self.cleanup_successful_items()
        
    def cleanup_successful_items(self):
        """清理成功处理的项目，保留失败、跳过和异常的条目"""
        cleanup_count = 0
        
        for step_num in self.step_data:
            items_to_remove = []
            for i, data_entry in enumerate(self.step_data[step_num]):
                values = data_entry['values']
                if len(values) > 0:
                    result = values[-1]  # 最后一列是结果
                    if result == '成功':
                        items_to_remove.append(i)
                        cleanup_count += 1
            
            # 从后往前删除，避免索引变化
            for i in reversed(items_to_remove):
                del self.step_data[step_num][i]
        
        print(f"[清理] 清理完成，共清理 {cleanup_count} 个成功处理的条目")
        
        # 显示剩余条目
        for step_num in self.step_data:
            remaining = len(self.step_data[step_num])
            if remaining > 0:
                print(f"[清理] 步骤{step_num}剩余 {remaining} 个条目")

def test_progress_tracking():
    """测试进度跟踪功能"""
    print("=" * 60)
    print("测试进度跟踪功能")
    print("=" * 60)
    
    # 创建模拟GUI
    mock_gui = MockGUI()
    
    # 测试各步骤的进度显示
    test_cases = [
        ("第一步：扫描并复制文件", "20250123/test.zip", "1/7 天, 1/5 项目"),
        ("第二步：解压压缩文件", "test.zip", "1/3 文件"),
        ("第三步：文件名和MSG.DAT信息读取", "B-1234_20250123", "1/2 文件夹"),
        ("第四步：FIM文件复制", "B-1234_20250123", "1/1 项目"),
        ("第五步：PC卡数据备份", "B-1234_20250123", "1/1 项目"),
        ("第六步：文件压缩", "B-1234_20250123 - test.dat", "压缩中 5/10 文件"),
    ]
    
    for step_text, current_item, progress_info in test_cases:
        mock_gui.update_current_step(step_text, current_item, progress_info)
        time.sleep(0.5)  # 模拟处理时间
    
    print("\n✅ 进度跟踪功能测试完成")

def test_cleanup_functionality():
    """测试清理功能"""
    print("\n" + "=" * 60)
    print("测试清理功能")
    print("=" * 60)
    
    # 创建模拟GUI
    mock_gui = MockGUI()
    
    # 添加测试数据
    test_data = [
        (1, "test1.zip", "成功"),
        (1, "test2.zip", "失败"),
        (2, "test3.zip", "成功"),
        (2, "test4.zip", "跳过"),
        (3, "test5", "成功"),
        (3, "test6", "异常"),
    ]
    
    print("[测试] 添加测试数据:")
    for step_num, data_name, result in test_data:
        mock_gui.add_step_result(step_num, data_name=data_name, result=result)
    
    # 显示清理前的状态
    print(f"\n[测试] 清理前各步骤条目数:")
    for step_num in mock_gui.step_data:
        count = len(mock_gui.step_data[step_num])
        if count > 0:
            print(f"  步骤{step_num}: {count} 个条目")
    
    # 启动清理任务
    print(f"\n[测试] 启动清理任务（5秒后执行）...")
    mock_gui.schedule_daily_cleanup()
    
    # 等待清理完成
    time.sleep(6)
    
    # 显示清理后的状态
    print(f"\n[测试] 清理后各步骤条目数:")
    for step_num in mock_gui.step_data:
        count = len(mock_gui.step_data[step_num])
        if count > 0:
            print(f"  步骤{step_num}: {count} 个条目")
            for data_entry in mock_gui.step_data[step_num]:
                values = data_entry['values']
                if len(values) >= 2:
                    print(f"    - {values[0]} ({values[-1]})")
    
    print("\n✅ 清理功能测试完成")

def test_compression_progress():
    """测试压缩进度显示"""
    print("\n" + "=" * 60)
    print("测试压缩进度显示")
    print("=" * 60)
    
    # 创建模拟GUI
    mock_gui = MockGUI()
    
    # 模拟压缩过程
    aircraft = "B-1234"
    date_info = "20250123"
    total_files = 15
    
    print(f"[压缩] 开始压缩 {aircraft}_{date_info}，共 {total_files} 个文件")
    
    for file_index in range(total_files):
        file_name = f"file_{file_index + 1:02d}.dat"
        compress_progress = f"压缩中 {file_index + 1}/{total_files} 文件"
        
        mock_gui.update_current_step(
            "第六步：文件压缩", 
            f"{aircraft}_{date_info} - {file_name}", 
            compress_progress
        )
        
        time.sleep(0.1)  # 模拟压缩时间
    
    print(f"\n✅ 压缩进度显示测试完成")

def main():
    """主测试函数"""
    print("开始测试新增功能")
    print("测试时间:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    # 初始化日志
    logger = setup_logging()
    
    try:
        # 测试进度跟踪
        test_progress_tracking()
        
        # 测试清理功能
        test_cleanup_functionality()
        
        # 测试压缩进度
        test_compression_progress()
        
        print("\n" + "=" * 60)
        print("🎉 所有测试完成！")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
